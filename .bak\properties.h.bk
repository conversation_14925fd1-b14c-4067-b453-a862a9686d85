#ifndef PROPERTIES_H
#define PROPERTIES_H

#include "attribute.h"
#include <memory>
#include <unordered_map>
#include <stdexcept>

class Properties
{
public:
    class iterator
    {
    private:
        typename std::unordered_map<std::string, std::shared_ptr<Value>>::iterator current;
        typename std::unordered_map<std::string, std::shared_ptr<Value>>::const_iterator const_current;

    public:
        iterator(typename std::unordered_map<std::string, std::shared_ptr<Value>>::iterator itr)
            : current(itr), first(itr->first), second(itr->second) {}
        iterator(typename std::unordered_map<std::string, std::shared_ptr<Value>>::const_iterator itr)
            : const_current(itr), first(itr->first), second(itr->second) {}
        iterator& operator++() { ++current; return *this; }
        iterator operator++(int) { iterator temp = *this; ++current; return temp; }
        const std::pair<const std::string, std::shared_ptr<Value>>& operator*() { return *current; }

        bool operator==(const iterator& other) { return current == other.current; }
        bool operator!=(const iterator& other) { return current != other.current; }

        bool operator==(const iterator& other) const { return const_current == other.current; }
        bool operator!=(const iterator& other) const { return const_current != other.current; }

        std::string first;
        Value second;
    };

    iterator begin() { return iterator(props_.begin()); }
    iterator end() { return iterator(props_.end()); }
    iterator begin() const { return iterator(props_.begin()); }
    iterator end() const { return iterator(props_.end()); }

    Value& operator[](const std::string& name) { return *props_[name]; }
    //const Value& operator[](const std::string& name) const { return getProperty(name); }
    const Value& at(const std::string& name) const { return getProperty(name); }

    const Value& getProperty(const std::string& name) const
    {
        auto it = props_.find(name);
        if (it != props_.end() && it->second) {
            return *it->second;
        } else {
            throw std::out_of_range("Property not found");
        }
    }

    size_t size() const { return props_.size(); }

    template<typename T>
    void insert(const std::string& name, const T& value)
    {
        props_.insert({ name, std::make_shared<Value>(value) });
        //std::cout << props_.at(name)->type().name() << std::endl;
    }

    void insert(const std::pair<std::string, Value>& value)
    {
        props_.insert({ value.first, std::make_shared<Value>(value.second) });
        //std::cout << props_.at(name)->type().name() << std::endl;
    }

    iterator find(const std::string& name) { return iterator(props_.find(name)); }
    iterator find(const std::string& name) const { return iterator(props_.find(name)); }

    bool contains(const std::string& name) const
    {
        auto it = props_.find(name);
        if (it != props_.end()) {
            return true;
        }
        return false;
    }

private:
    std::unordered_map<std::string, std::shared_ptr<Value>> props_;
};

#endif // PROPERTIES_H
