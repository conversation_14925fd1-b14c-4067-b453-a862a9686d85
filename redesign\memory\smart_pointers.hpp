/**
 * @file smart_pointers.hpp
 * @brief 现代化智能指针和内存管理
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "../foundation/types.hpp"
#include "../foundation/utility.hpp"
#include <memory>
#include <atomic>
#include <utility>

namespace property::memory {

using namespace foundation;

/// @brief 原子引用计数器
class AtomicRefCount {
public:
    constexpr AtomicRefCount() noexcept : count_(1) {}
    
    /// @brief 增加引用计数
    void add_ref() noexcept {
        count_.fetch_add(1, std::memory_order_relaxed);
    }
    
    /// @brief 减少引用计数，返回是否应该删除对象
    [[nodiscard]] bool release() noexcept {
        return count_.fetch_sub(1, std::memory_order_acq_rel) == 1;
    }
    
    /// @brief 获取当前引用计数
    [[nodiscard]] i32 use_count() const noexcept {
        return count_.load(std::memory_order_acquire);
    }
    
    /// @brief 是否唯一引用
    [[nodiscard]] bool unique() const noexcept {
        return use_count() == 1;
    }

private:
    std::atomic<i32> count_;
};

/// @brief 侵入式引用计数基类
class RefCountedBase {
public:
    constexpr RefCountedBase() noexcept = default;
    virtual ~RefCountedBase() = default;
    
    /// @brief 增加引用计数
    void add_ref() const noexcept {
        ref_count_.add_ref();
    }
    
    /// @brief 减少引用计数
    void release() const noexcept {
        if (ref_count_.release()) {
            delete this;
        }
    }
    
    /// @brief 获取引用计数
    [[nodiscard]] i32 use_count() const noexcept {
        return ref_count_.use_count();
    }
    
    /// @brief 是否唯一引用
    [[nodiscard]] bool unique() const noexcept {
        return ref_count_.unique();
    }
    
    DISABLE_COPY_MOVE(RefCountedBase)

private:
    mutable AtomicRefCount ref_count_;
};

/// @brief 侵入式智能指针
template<typename T>
class intrusive_ptr {
public:
    using element_type = T;
    using pointer = T*;
    using reference = T&;
    
    /// @brief 默认构造函数
    constexpr intrusive_ptr() noexcept = default;
    
    /// @brief 从裸指针构造
    explicit intrusive_ptr(pointer ptr, bool add_ref = true) noexcept : ptr_(ptr) {
        if (ptr_ && add_ref) {
            ptr_->add_ref();
        }
    }
    
    /// @brief 拷贝构造函数
    intrusive_ptr(const intrusive_ptr& other) noexcept : ptr_(other.ptr_) {
        if (ptr_) {
            ptr_->add_ref();
        }
    }
    
    /// @brief 移动构造函数
    intrusive_ptr(intrusive_ptr&& other) noexcept : ptr_(std::exchange(other.ptr_, nullptr)) {}
    
    /// @brief 析构函数
    ~intrusive_ptr() {
        if (ptr_) {
            ptr_->release();
        }
    }
    
    /// @brief 拷贝赋值运算符
    intrusive_ptr& operator=(const intrusive_ptr& other) noexcept {
        if (this != &other) {
            intrusive_ptr temp(other);
            swap(temp);
        }
        return *this;
    }
    
    /// @brief 移动赋值运算符
    intrusive_ptr& operator=(intrusive_ptr&& other) noexcept {
        if (this != &other) {
            intrusive_ptr temp(std::move(other));
            swap(temp);
        }
        return *this;
    }
    
    /// @brief 重置指针
    void reset(pointer ptr = nullptr, bool add_ref = true) noexcept {
        intrusive_ptr temp(ptr, add_ref);
        swap(temp);
    }
    
    /// @brief 释放指针所有权
    [[nodiscard]] pointer release() noexcept {
        return std::exchange(ptr_, nullptr);
    }
    
    /// @brief 获取裸指针
    [[nodiscard]] pointer get() const noexcept { return ptr_; }
    
    /// @brief 解引用运算符
    [[nodiscard]] reference operator*() const noexcept {
        ASSERT(ptr_ != nullptr);
        return *ptr_;
    }
    
    /// @brief 成员访问运算符
    [[nodiscard]] pointer operator->() const noexcept {
        ASSERT(ptr_ != nullptr);
        return ptr_;
    }
    
    /// @brief 布尔转换运算符
    [[nodiscard]] explicit operator bool() const noexcept {
        return ptr_ != nullptr;
    }
    
    /// @brief 获取引用计数
    [[nodiscard]] i32 use_count() const noexcept {
        return ptr_ ? ptr_->use_count() : 0;
    }
    
    /// @brief 是否唯一引用
    [[nodiscard]] bool unique() const noexcept {
        return ptr_ && ptr_->unique();
    }
    
    /// @brief 交换
    void swap(intrusive_ptr& other) noexcept {
        std::swap(ptr_, other.ptr_);
    }
    
    /// @brief 比较运算符
    [[nodiscard]] bool operator==(const intrusive_ptr& other) const noexcept {
        return ptr_ == other.ptr_;
    }
    
    [[nodiscard]] bool operator!=(const intrusive_ptr& other) const noexcept {
        return !(*this == other);
    }
    
    [[nodiscard]] bool operator<(const intrusive_ptr& other) const noexcept {
        return ptr_ < other.ptr_;
    }

private:
    pointer ptr_ = nullptr;
};

/// @brief 创建侵入式智能指针
template<typename T, typename... Args>
[[nodiscard]] intrusive_ptr<T> make_intrusive(Args&&... args) {
    return intrusive_ptr<T>(new T(std::forward<Args>(args)...), false);
}

/// @brief 交换函数
template<typename T>
void swap(intrusive_ptr<T>& lhs, intrusive_ptr<T>& rhs) noexcept {
    lhs.swap(rhs);
}

/// @brief 写时复制智能指针
template<typename T>
class cow_ptr {
public:
    using element_type = T;
    using pointer = T*;
    using reference = T&;
    using const_reference = const T&;
    
    /// @brief 默认构造函数
    cow_ptr() : ptr_(std::make_shared<T>()) {}
    
    /// @brief 从值构造
    explicit cow_ptr(const T& value) : ptr_(std::make_shared<T>(value)) {}
    
    /// @brief 从值构造（移动）
    explicit cow_ptr(T&& value) : ptr_(std::make_shared<T>(std::move(value))) {}
    
    /// @brief 获取只读引用
    [[nodiscard]] const_reference operator*() const noexcept {
        return *ptr_;
    }
    
    /// @brief 获取只读指针
    [[nodiscard]] const T* operator->() const noexcept {
        return ptr_.get();
    }
    
    /// @brief 获取可写引用（触发写时复制）
    [[nodiscard]] reference mutable_ref() {
        detach();
        return *ptr_;
    }
    
    /// @brief 获取可写指针（触发写时复制）
    [[nodiscard]] pointer mutable_ptr() {
        detach();
        return ptr_.get();
    }
    
    /// @brief 是否共享
    [[nodiscard]] bool is_shared() const noexcept {
        return ptr_.use_count() > 1;
    }
    
    /// @brief 获取引用计数
    [[nodiscard]] i64 use_count() const noexcept {
        return ptr_.use_count();
    }
    
    /// @brief 分离（写时复制）
    void detach() {
        if (is_shared()) {
            ptr_ = std::make_shared<T>(*ptr_);
        }
    }

private:
    std::shared_ptr<T> ptr_;
};

/// @brief 创建写时复制智能指针
template<typename T, typename... Args>
[[nodiscard]] cow_ptr<T> make_cow(Args&&... args) {
    return cow_ptr<T>(T(std::forward<Args>(args)...));
}

/// @brief 内存池分配器
template<typename T, size_type BlockSize = 1024>
class pool_allocator {
public:
    using value_type = T;
    using size_type = foundation::size_type;
    using difference_type = std::ptrdiff_t;
    
    pool_allocator() = default;
    
    template<typename U>
    pool_allocator(const pool_allocator<U, BlockSize>&) noexcept {}
    
    [[nodiscard]] T* allocate(size_type n) {
        if (n > BlockSize) {
            return static_cast<T*>(std::malloc(n * sizeof(T)));
        }
        
        if (!current_block_ || current_offset_ + n > BlockSize) {
            allocate_new_block();
        }
        
        T* result = current_block_ + current_offset_;
        current_offset_ += n;
        return result;
    }
    
    void deallocate(T* ptr, size_type n) noexcept {
        if (n > BlockSize) {
            std::free(ptr);
        }
        // 对于池分配的内存，我们不立即释放
    }
    
    template<typename U>
    [[nodiscard]] bool operator==(const pool_allocator<U, BlockSize>&) const noexcept {
        return true;
    }

private:
    void allocate_new_block() {
        auto block = static_cast<T*>(std::malloc(BlockSize * sizeof(T)));
        blocks_.emplace_back(block, [](T* p) { std::free(p); });
        current_block_ = block;
        current_offset_ = 0;
    }
    
    std::vector<std::unique_ptr<T, void(*)(T*)>> blocks_;
    T* current_block_ = nullptr;
    size_type current_offset_ = 0;
};

} // namespace property::memory
