/**
 * @file property_tests.cpp
 * @brief 属性系统单元测试
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#include "../property.hpp"
#include <cassert>
#include <iostream>
#include <sstream>
#include <chrono>

using namespace property;

/// @brief 简单测试框架
class TestFramework {
public:
    static void run_test(const string& name, std::function<void()> test_func) {
        std::cout << "Running test: " << name << " ... ";
        try {
            test_func();
            std::cout << "PASSED" << std::endl;
            ++passed_count_;
        } catch (const std::exception& e) {
            std::cout << "FAILED: " << e.what() << std::endl;
            ++failed_count_;
        } catch (...) {
            std::cout << "FAILED: Unknown exception" << std::endl;
            ++failed_count_;
        }
        ++total_count_;
    }
    
    static void print_summary() {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Total: " << total_count_ << std::endl;
        std::cout << "Passed: " << passed_count_ << std::endl;
        std::cout << "Failed: " << failed_count_ << std::endl;
        std::cout << "Success Rate: " << (total_count_ > 0 ? (passed_count_ * 100 / total_count_) : 0) << "%" << std::endl;
    }

private:
    static inline size_type total_count_ = 0;
    static inline size_type passed_count_ = 0;
    static inline size_type failed_count_ = 0;
};

#define TEST(name) \
    TestFramework::run_test(#name, []()

#define ASSERT_EQ(expected, actual) \
    do { \
        if ((expected) != (actual)) { \
            throw std::runtime_error("Assertion failed: " #expected " != " #actual); \
        } \
    } while(0)

#define ASSERT_TRUE(condition) \
    do { \
        if (!(condition)) { \
            throw std::runtime_error("Assertion failed: " #condition " is false"); \
        } \
    } while(0)

#define ASSERT_FALSE(condition) \
    do { \
        if (condition) { \
            throw std::runtime_error("Assertion failed: " #condition " is true"); \
        } \
    } while(0)

/// @brief 基础属性测试
void test_basic_properties() {
    TEST(basic_property_creation) {
        auto prop = make_property<i32>("test", 42);
        ASSERT_EQ(42, prop.get());
        ASSERT_EQ("test", prop.name());
        ASSERT_FALSE(prop.has_binding());
        ASSERT_FALSE(prop.is_readonly());
    });
    
    TEST(property_value_setting) {
        auto prop = make_property<string>("name", "initial");
        ASSERT_EQ("initial", prop.get());
        
        prop.set("changed");
        ASSERT_EQ("changed", prop.get());
        
        prop = "assigned";
        ASSERT_EQ("assigned", prop.get());
    });
    
    TEST(readonly_property) {
        auto prop = make_readonly_property<i32>("readonly", 100);
        ASSERT_EQ(100, prop.get());
        ASSERT_TRUE(prop.is_readonly());
        
        // 尝试设置值应该被忽略
        prop.set(200);
        ASSERT_EQ(100, prop.get()); // 值不应该改变
    });
}

/// @brief 绑定系统测试
void test_binding_system() {
    TEST(simple_binding) {
        auto source = make_property<i32>("source", 10);
        auto target = make_property<i32>("target");
        
        bind_to(target, [&]() { return source.get() * 2; });
        
        ASSERT_TRUE(target.has_binding());
        ASSERT_EQ(20, target.get());
        
        source = 15;
        ASSERT_EQ(30, target.get());
    });
    
    TEST(complex_binding) {
        auto x = make_property<f64>("x", 3.0);
        auto y = make_property<f64>("y", 4.0);
        auto distance = make_property<f64>("distance");
        
        bind_to(distance, [&]() {
            return std::sqrt(x.get() * x.get() + y.get() * y.get());
        });
        
        ASSERT_EQ(5.0, distance.get());
        
        x = 6.0;
        y = 8.0;
        ASSERT_EQ(10.0, distance.get());
    });
    
    TEST(binding_chain) {
        auto a = make_property<i32>("a", 1);
        auto b = make_property<i32>("b");
        auto c = make_property<i32>("c");
        
        bind_to(b, [&]() { return a.get() + 10; });
        bind_to(c, [&]() { return b.get() * 2; });
        
        ASSERT_EQ(11, b.get());
        ASSERT_EQ(22, c.get());
        
        a = 5;
        ASSERT_EQ(15, b.get());
        ASSERT_EQ(30, c.get());
    });
}

/// @brief 观察者系统测试
void test_observer_system() {
    TEST(simple_observer) {
        auto prop = make_property<i32>("observed", 0);
        i32 notification_count = 0;
        
        auto observer = observe(prop, [&]() {
            ++notification_count;
        });
        
        ASSERT_EQ(0, notification_count);
        
        prop = 1;
        ASSERT_EQ(1, notification_count);
        
        prop = 2;
        ASSERT_EQ(2, notification_count);
    });
    
    TEST(multiple_observers) {
        auto prop = make_property<string>("multi", "start");
        i32 count1 = 0, count2 = 0;
        
        auto obs1 = observe(prop, [&]() { ++count1; });
        auto obs2 = observe(prop, [&]() { ++count2; });
        
        prop = "change1";
        ASSERT_EQ(1, count1);
        ASSERT_EQ(1, count2);
        
        prop = "change2";
        ASSERT_EQ(2, count1);
        ASSERT_EQ(2, count2);
    });
    
    TEST(observer_priority) {
        auto prop = make_property<i32>("priority", 0);
        vector<i32> execution_order;
        
        auto low_obs = make_simple_observer([&]() { execution_order.push_back(1); }, ObserverPriority::Low);
        auto high_obs = make_simple_observer([&]() { execution_order.push_back(2); }, ObserverPriority::High);
        auto normal_obs = make_simple_observer([&]() { execution_order.push_back(3); }, ObserverPriority::Normal);
        
        prop.add_observer(low_obs);
        prop.add_observer(high_obs);
        prop.add_observer(normal_obs);
        
        prop = 42;
        
        // 高优先级应该先执行
        ASSERT_TRUE(execution_order.size() >= 3);
        ASSERT_EQ(2, execution_order[0]); // High priority first
    });
}

/// @brief 高级功能测试
void test_advanced_features() {
    TEST(property_group) {
        auto prop1 = make_property<i32>("prop1", 1);
        auto prop2 = make_property<i32>("prop2", 2);
        auto prop3 = make_property<i32>("prop3", 3);
        
        auto group = make_property_group(prop1, prop2, prop3);
        
        i32 total_notifications = 0;
        group.observe_all([&]() { ++total_notifications; });
        
        prop1 = 10;
        prop2 = 20;
        prop3 = 30;
        
        ASSERT_EQ(3, total_notifications);
    });
    
    TEST(property_validator) {
        auto prop = make_property<i32>("validated", 5);
        bool validation_failed = false;
        
        auto validator = make_validator(prop, [](i32 value) {
            return value >= 0 && value <= 100;
        }, "Value must be between 0 and 100");
        
        validator.set_error_handler([&](i32 invalid_value, string_view msg) {
            validation_failed = true;
        });
        
        prop = 50; // Valid
        ASSERT_EQ(50, prop.get());
        ASSERT_FALSE(validation_failed);
        
        prop = 150; // Invalid
        ASSERT_EQ(50, prop.get()); // Should remain unchanged
        ASSERT_TRUE(validation_failed);
    });
    
    TEST(property_converter) {
        auto celsius = make_property<f64>("celsius", 0.0);
        auto fahrenheit = make_property<f64>("fahrenheit");
        
        auto converter = make_converter(celsius, fahrenheit, [](f64 c) {
            return c * 9.0 / 5.0 + 32.0;
        });
        
        ASSERT_EQ(32.0, fahrenheit.get()); // 0°C = 32°F
        
        celsius = 100.0;
        ASSERT_EQ(212.0, fahrenheit.get()); // 100°C = 212°F
    });
}

/// @brief 性能测试
void test_performance() {
    TEST(property_creation_performance) {
        auto start = std::chrono::high_resolution_clock::now();
        
        constexpr size_type count = 10000;
        vector<IntProperty> properties;
        properties.reserve(count);
        
        for (size_type i = 0; i < count; ++i) {
            properties.emplace_back("prop" + std::to_string(i), static_cast<i32>(i));
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "Created " << count << " properties in " << duration.count() << " microseconds" << std::endl;
        ASSERT_TRUE(duration.count() < 100000); // Should be less than 100ms
    });
    
    TEST(binding_evaluation_performance) {
        auto source = make_property<i32>("source", 1);
        auto target = make_property<i32>("target");
        
        bind_to(target, [&]() { return source.get() * 2; });
        
        auto start = std::chrono::high_resolution_clock::now();
        
        constexpr size_type iterations = 100000;
        for (size_type i = 0; i < iterations; ++i) {
            source = static_cast<i32>(i);
            volatile auto value = target.get(); // Prevent optimization
            (void)value;
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "Performed " << iterations << " binding evaluations in " << duration.count() << " microseconds" << std::endl;
    });
}

/// @brief 主测试函数
int main() {
    std::cout << "=== Property System Tests ===" << std::endl;
    std::cout << "Version: " << PROPERTY_VERSION << std::endl;
    std::cout << std::endl;
    
    test_basic_properties();
    test_binding_system();
    test_observer_system();
    test_advanced_features();
    test_performance();
    
    TestFramework::print_summary();
    
    return 0;
}
