#ifndef ATTRIBUTE_H
#define ATTRIBUTE_H

#include <typeinfo>
#include <iostream>

class Value
{
public:
    Value()
        : m_data(nullptr) {}

    template<typename T>
    Value(const T& value)
    {
        m_data = new Variant<T>(value);
    }

    ~Value()
    {
        if (m_data) {
            delete m_data;
        }
    }

    Value(const Value& other)
    {
        m_data = other.m_data ? other.m_data->clone() : nullptr;
    }

    Value& operator=(const Value& other)
    {
        if (this != &other) {
            if (m_data) {
                delete m_data;
            }
            m_data = other.m_data ? other.m_data->clone() : nullptr;
        }
        return *this;
    }

    template<typename T>
    Value& operator=(const T& other)
    {
        if (m_data && (m_data->type() != typeid(Variant<T>) || static_cast<Variant<T>*>(m_data)->value != other)) {
            delete m_data;
            m_data = new Variant<T>(other);
        }
        return *this;
    }

    /*template<typename T>
    bool operator==(T& other)
    {
        return m_data && (m_data->type() == typeid(Variant<T>)) && static_cast<Variant<T>*>(m_data)->value == other;
    }*/

    template<typename T>
    bool operator==(const T& other) const
    {
        return m_data && (typeid(*m_data) == typeid(Variant<T>)) && static_cast<Variant<T>*>(m_data)->value == other;
    }

    bool isVaild() const
    {
        return m_data != nullptr;
    }

    const std::type_info& type() const
    {
        if (m_data) {
            return m_data->type();
        } else {
            return typeid(nullptr);
        }
    }

    template<typename T>
    T& get() const
    {
        //std::cout << "get: " << m_data->type().name() << typeid(T).name();
        if (m_data && m_data->type() == typeid(T)) {
            return static_cast<Variant<T>*>(m_data)->value;
        } else {
            std::cout << "Type mismatch." << std::endl;
            throw std::bad_cast();
        }
    }

    /*template<typename T>
    const T& const_get() const
    {
        //std::cout << "get: " << m_data->type().name() << typeid(T).name();
        if (m_data && m_data->type() == typeid(T)) {
            return static_cast<Variant<T>*>(m_data)->value;
        } else {
            std::cout << "Type mismatch." << std::endl;
            throw std::bad_cast();
        }
    }*/

private:
    class V
    {
    public:
        virtual ~V() {}
        virtual const std::type_info& type() const = 0;
        virtual V* clone() const = 0;
    };

    template<typename T>
    class Variant : public V
    {
    public:
        Variant(const T& _value)
            : value(_value) {}
        const std::type_info& type() const override
        {
            return typeid(T);
        }

        V* clone() const override
        {
            return new Variant<T>(*this);
        }

        T value;
    };

    V* m_data;
};

namespace value {
template<typename T>
T& get(const Value& v)
{
    if (v.type() == typeid(T)) {
        return v.get<T>();
    } else {
        std::cout << "value::get: Type mismatch.\n--current:"
                  << v.type().name()
                  << "\n--target: "
                  << typeid(T).name() << std::endl;
        throw std::bad_cast();
    }
}

/*template<typename T>
const T& const_get(const Value& v)
{
    if (v.type() == typeid(T)) {
        return v.const_get<T>();
    } else {
        std::cout << "value::get: Type mismatch.\n--current:"
                  << v.type().name()
                  << "\n--target: "
                  << typeid(T).name() << std::endl;
        throw std::bad_cast();
    }
}*/
}
#endif // ATTRIBUTE_H
