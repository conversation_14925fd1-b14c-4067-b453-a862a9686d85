﻿#ifndef ATTRIBUTE_H
#define ATTRIBUTE_H

#include <functional>

// #include "variant.h"

namespace attribute {

template <typename T>
class Attribute {
public:
    Attribute() = default;
    Attribute(const T& value)
        : m_value(value) {}

    ~Attribute() {
        unbind();
    }

    inline bool operator==(const Attribute& other) noexcept {
        if (m_value == other.m_value) {
            return true;
        }
        return false;
    }

    inline T value() {
        if (m_isBound && m_dirty) {
            updateValue();
        }
        return m_value;
    }

    inline void setValue(const T& value) {
        unbind();
        m_value = value;
        notifyDependents();
    }

    inline void setValue(const std::vector<Attribute*>& dependencies,
                  const std::function<T()>& calculator) {
        bind(dependencies, calculator);
    }

    inline void unbind() {
        if (m_isBound) {
            for (const auto& dep : dependencies_) {
                dep->removeDependent(this);
            }
            dependencies_.clear();
            m_isBound = false;
            calculator_ = nullptr;
            //m_value.reset();
        }
    }

protected:
    T m_value;
    bool m_isBound { false };
    bool m_dirty { false };

    std::vector<Attribute*> dependents_;
    std::vector<Attribute*> dependencies_;
    std::function<T()> calculator_;

    inline void updateValue() {
        if (m_isBound) {
            m_value = calculator_();
        }
        m_dirty = false;
    }

    inline void bind(const std::vector<Attribute*>& dependencies,
              const std::function<T()>& calculator) {
        unbind();
        m_isBound = true;
        calculator_ = calculator;
        for (const auto& dep : dependencies) {
            dep->addDependent(this);
            dependencies_.push_back(dep);
        }
        markDirty();
    }

    inline void notifyDependents() {
        for (auto it = dependents_.begin(); it != dependents_.end();) {
            if (auto dependent = *it) {
                dependent->markDirty();
                ++it;
            } else {
                it = dependents_.erase(it);
            }
        }
    }

    inline void markDirty() {
        if (!m_dirty) {
            m_dirty = true;
            notifyDependents();
        }
    }

    inline void addDependent(Attribute* dependent) {
        dependents_.push_back(dependent);
    }

    inline void removeDependent(Attribute* dependent) {
        dependents_.erase(
            std::remove_if(dependents_.begin(), dependents_.end(),
                           [&](Attribute* dep) {
                               return dep == dependent;
                           }
                           ),
            dependents_.end()
            );
    }
};

// class Number : public Attribute {
// public:
//     enum class NumberType {
//         Integer,
//         Float,
//         Double,
//         LongLong,
//         None
//     };

//     Number(const std::string& name, const Variant value)
//         : Attribute(name, Type::T_Number, value) {
//         setValue(value);
//     }
// };

// class Text : public Attribute {
//     Text(const std::string& name, const std::string& value)
//         : Attribute(name, Type::T_Text, value) {
//         setValue(value);
//     }

//     std::string value() {
//         if (m_isBound && m_dirty) {
//             updateValue();
//         }
//         return !m_value.isEmpty() ? m_value.value<std::string>() : m_defaultValue;
//     }
// protected:
//     std::string m_defaultValue;
// };

// class List : public Attribute {
//     List(const std::string& name, const std::vector<Variant> value)
//         : Attribute(name, Type::T_List, value) {
//         setValue(value);
//     }

//     virtual Variant value() {
//         if (m_isBound && m_dirty) {
//             updateValue();
//         }
//         return !m_value.isEmpty() ? m_value : m_defaultValue;
//     }

// };
/*class Int : public Number {
public:
    Int(int value) : m_value(value) {}

    Type type() const override { return Type::T_Int; }
    std::string name() const { return m_name; }

    int value() const { return m_value; }
    void setValue(int val) { m_value = val; notify(); }

    // 绑定相关操作
    void bind(const std::function<int()> &callback) {
        bindCallback = callback;
        m_value = callback();
        notify();
    }

    void notify() const override {
        if (onChange) {
            onChange();
        }
    }

    void onChangeBind(std::function<void()> &callback) {
        onChange = callback;
    }

private:
    std::string m_name;
    int m_value;
    std::function<int()> bindCallback;
    std::function<void()> onChange;
};


class Float : public Number {
public:
    Float(double value) : m_value(value) {}

    Type type() const override { return Type::T_Float; }

    double value() const { return m_value; }
    void setValue(double val) { m_value = val; notify(); }

    // 绑定相关操作
    void bind(const std::function<double()> &callback) {
        bindCallback = callback;
        m_value = callback();
        notify();
    }

    void notify() const override  {
        if (onChange) {
            onChange();
        }
    }

    void onChangeBind(std::function<void()> &callback) {
        onChange = callback;
    }

private:
    double m_value;
    std::function<double()> bindCallback;
    std::function<void()> onChange;
};*/


}
#endif // ATTRIBUTE_H
