/**
 * @file simple_binding.hpp
 * @brief 简化的绑定系统实现
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "../foundation/types.hpp"
#include "../foundation/utility.hpp"
#include "../core/property_base.hpp"
#include "../memory/smart_pointers.hpp"
#include <functional>
#include <concepts>
#include <atomic>
#include <mutex>
#include <unordered_map>
#include <algorithm>

namespace property::binding {

using namespace foundation;
using namespace core;
using namespace memory;

/// @brief 可通知接口
class INotifiable {
public:
    virtual ~INotifiable() = default;
    virtual void on_dependency_changed() = 0;
};

/// @brief 属性注册表 - 管理属性的生命周期
class PropertyRegistry {
public:
    /// @brief 获取全局实例
    [[nodiscard]] static PropertyRegistry& instance() {
        static PropertyRegistry registry;
        return registry;
    }

    /// @brief 注册属性
    void register_property(const IProperty* prop, std::shared_ptr<IProperty> shared_prop) {
        std::lock_guard lock(mutex_);
        properties_[prop] = std::move(shared_prop);
    }

    /// @brief 注销属性
    void unregister_property(const IProperty* prop) {
        std::lock_guard lock(mutex_);
        properties_.erase(prop);
    }

    /// @brief 获取属性的shared_ptr
    [[nodiscard]] std::weak_ptr<IProperty> get_property_ptr(const IProperty* prop) const {
        std::lock_guard lock(mutex_);
        auto it = properties_.find(prop);
        if (it != properties_.end()) {
            return it->second;
        }
        return std::weak_ptr<IProperty>{};
    }

private:
    mutable std::mutex mutex_;
    std::unordered_map<const IProperty*, std::shared_ptr<IProperty>> properties_;
};

/// @brief 绑定状态
enum class BindingState {
    Clean,      ///< 绑定是干净的，值是最新的
    Dirty,      ///< 绑定是脏的，需要重新计算
    Evaluating, ///< 正在计算中
    Error       ///< 计算出错
};

/// @brief 绑定错误信息
struct BindingError {
    string message;
    SourceLocation location;
    
    BindingError(string_view msg = {}, const SourceLocation& loc = {})
        : message(msg), location(loc) {}
};

/// @brief 依赖追踪器 - 简化版本
class DependencyTracker {
public:
    /// @brief 获取当前线程的依赖追踪器
    [[nodiscard]] static DependencyTracker& current() {
        thread_local DependencyTracker tracker;
        return tracker;
    }

    /// @brief 开始依赖收集
    void begin_collection() {
        collecting_ = true;
        current_dependencies_.clear();
        current_raw_dependencies_.clear();
    }

    /// @brief 结束依赖收集
    [[nodiscard]] vector<std::weak_ptr<IProperty>> end_collection() {
        collecting_ = false;
        return std::move(current_dependencies_);
    }

    /// @brief 结束依赖收集（裸指针版本）
    [[nodiscard]] vector<const IProperty*> end_collection_raw() {
        collecting_ = false;
        return std::move(current_raw_dependencies_);
    }

    /// @brief 添加依赖
    void add_dependency(std::weak_ptr<IProperty> property) {
        if (collecting_) {
            current_dependencies_.push_back(std::move(property));
        }
    }

    /// @brief 添加依赖（裸指针版本）
    void add_dependency_raw(const IProperty* property) {
        if (collecting_) {
            current_raw_dependencies_.push_back(property);
        }
    }

    /// @brief 是否正在收集依赖
    [[nodiscard]] bool is_collecting() const noexcept {
        return collecting_;
    }

private:
    bool collecting_ = false;
    vector<std::weak_ptr<IProperty>> current_dependencies_;
    vector<const IProperty*> current_raw_dependencies_;
};

/// @brief 绑定接口
template<typename T>
class IBinding {
public:
    virtual ~IBinding() = default;
    
    /// @brief 计算绑定值
    [[nodiscard]] virtual T evaluate() = 0;
    
    /// @brief 获取绑定状态
    [[nodiscard]] virtual BindingState state() const noexcept = 0;
    
    /// @brief 标记为脏
    virtual void mark_dirty() noexcept = 0;
    
    /// @brief 是否有错误
    [[nodiscard]] virtual bool has_error() const noexcept = 0;
    
    /// @brief 获取错误信息
    [[nodiscard]] virtual const BindingError& error() const noexcept = 0;
    
    /// @brief 获取依赖的属性
    [[nodiscard]] virtual vector<std::weak_ptr<IProperty>> dependencies() const = 0;
};

/// @brief 函数绑定实现
template<typename T, typename F>
class FunctionBinding : public IBinding<T> {
    static_assert(BindingFunction<F, T>, "F must be a valid binding function for type T");
public:
    explicit FunctionBinding(F&& func, const SourceLocation& loc = {})
        : function_(std::forward<F>(func))
        , location_(loc)
        , state_(BindingState::Dirty) {}
    
    /// @brief 计算绑定值
    [[nodiscard]] T evaluate() override {
        if (state_ == BindingState::Evaluating) {
            throw std::runtime_error("Circular dependency detected");
        }
        
        if (state_ == BindingState::Clean) {
            return cached_value_;
        }
        
        state_ = BindingState::Evaluating;
        
        try {
            // 开始依赖收集
            auto& tracker = DependencyTracker::current();
            tracker.begin_collection();

            // 执行绑定函数，此时会自动收集依赖
            cached_value_ = function_();

            // 结束依赖收集
            raw_dependencies_ = tracker.end_collection_raw();

            error_ = BindingError{}; // 清除错误
            state_ = BindingState::Clean;

            return cached_value_;
        } catch (const std::exception& e) {
            state_ = BindingState::Error;
            error_ = BindingError{e.what(), location_};
            throw;
        }
    }
    
    /// @brief 获取绑定状态
    [[nodiscard]] BindingState state() const noexcept override {
        return state_;
    }
    
    /// @brief 标记为脏
    void mark_dirty() noexcept override {
        state_ = BindingState::Dirty;
    }
    
    /// @brief 是否有错误
    [[nodiscard]] bool has_error() const noexcept override {
        return state_ == BindingState::Error;
    }
    
    /// @brief 获取错误信息
    [[nodiscard]] const BindingError& error() const noexcept override {
        return error_;
    }
    
    /// @brief 获取依赖的属性
    [[nodiscard]] vector<std::weak_ptr<IProperty>> dependencies() const override {
        return dependencies_;
    }

    /// @brief 获取依赖的属性（裸指针版本）
    [[nodiscard]] const vector<const IProperty*>& raw_dependencies() const {
        return raw_dependencies_;
    }

private:
    F function_;
    SourceLocation location_;
    std::atomic<BindingState> state_;
    mutable T cached_value_{};
    BindingError error_;
    vector<std::weak_ptr<IProperty>> dependencies_;
    vector<const IProperty*> raw_dependencies_;
};

/// @brief 依赖管理器 - 全局单例，使用裸指针简化实现
class DependencyManager {
public:
    /// @brief 获取全局实例
    [[nodiscard]] static DependencyManager& instance() {
        static DependencyManager manager;
        return manager;
    }

    /// @brief 注册依赖关系
    void register_dependency(IProperty* source, INotifiable* dependent) {
        std::lock_guard lock(mutex_);
        dependents_[source].push_back(dependent);
    }

    /// @brief 移除依赖关系
    void remove_dependency(IProperty* source, INotifiable* dependent) {
        std::lock_guard lock(mutex_);
        auto it = dependents_.find(source);
        if (it != dependents_.end()) {
            it->second.erase(
                std::remove(it->second.begin(), it->second.end(), dependent),
                it->second.end()
            );
        }
    }

    /// @brief 通知依赖属性
    void notify_dependents(IProperty* source) {
        std::lock_guard lock(mutex_);
        auto it = dependents_.find(source);
        if (it != dependents_.end()) {
            // 通知所有依赖属性
            for (auto* dependent : it->second) {
                if (dependent) {
                    dependent->on_dependency_changed();
                }
            }
        }
    }

    /// @brief 清除所有依赖
    void clear_dependencies(IProperty* source) {
        std::lock_guard lock(mutex_);
        dependents_.erase(source);
    }

    /// @brief 清除特定依赖属性的所有依赖关系
    void clear_dependent(INotifiable* dependent) {
        std::lock_guard lock(mutex_);
        for (auto& [source, deps] : dependents_) {
            deps.erase(
                std::remove(deps.begin(), deps.end(), dependent),
                deps.end()
            );
        }
    }

private:
    std::mutex mutex_;
    std::unordered_map<IProperty*, vector<INotifiable*>> dependents_;
};

} // namespace property::binding
