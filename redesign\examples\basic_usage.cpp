/**
 * @file basic_usage.cpp
 * @brief 属性系统基本使用示例
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#include "../property.hpp"
#include <iostream>
#include <iomanip>

using namespace property;

/// @brief 人员类示例
class Person {
public:
    // 声明属性
    DECLARE_PROPERTY(string, first_name);
    DECLARE_PROPERTY(string, last_name);
    DECLARE_PROPERTY(i32, age);
    DECLARE_READONLY_PROPERTY(string, full_name);
    DECLARE_READONLY_PROPERTY(bool, is_adult);
    
    Person(string_view first, string_view last, i32 age_val) {
        // 设置初始值
        first_name = string(first);
        last_name = string(last);
        age = age_val;
        
        // 设置绑定
        BIND_PROPERTY(full_name, first_name.get() + " " + last_name.get());
        BIND_PROPERTY(is_adult, age.get() >= 18);
        
        // 添加观察者
        OBSERVE_PROPERTY(age, [this]() {
            std::cout << "Age changed to: " << age.get() << std::endl;
        });
        
        OBSERVE_PROPERTY(full_name, [this]() {
            std::cout << "Full name is now: " << full_name.get() << std::endl;
        });
    }
};

/// @brief 温度转换器示例
class TemperatureConverter {
public:
    Property<f64> celsius{"celsius", 0.0};
    Property<f64> fahrenheit{"fahrenheit"};
    Property<f64> kelvin{"kelvin"};
    
    TemperatureConverter() {
        // 设置双向绑定
        bind_to(fahrenheit, [this]() {
            return celsius.get() * 9.0 / 5.0 + 32.0;
        });
        
        bind_to(kelvin, [this]() {
            return celsius.get() + 273.15;
        });
        
        // 添加观察者
        auto temp_observer = observe(celsius, [this]() {
            std::cout << std::fixed << std::setprecision(2)
                      << "Temperature: " << celsius.get() << "°C = "
                      << fahrenheit.get() << "°F = "
                      << kelvin.get() << "K" << std::endl;
        });
    }
};

/// @brief 计算器示例
class Calculator {
public:
    Property<f64> operand1{"operand1", 0.0};
    Property<f64> operand2{"operand2", 0.0};
    Property<string> operation{"operation", "+"};
    
    Property<f64> result{"result"};
    Property<string> expression{"expression"};
    
    Calculator() {
        // 设置结果绑定
        bind_to(result, [this]() {
            const auto& op = operation.get();
            auto a = operand1.get();
            auto b = operand2.get();
            
            if (op == "+") return a + b;
            if (op == "-") return a - b;
            if (op == "*") return a * b;
            if (op == "/") return b != 0.0 ? a / b : 0.0;
            return 0.0;
        });
        
        // 设置表达式绑定
        bind_to(expression, [this]() {
            return std::to_string(operand1.get()) + " " + 
                   operation.get() + " " + 
                   std::to_string(operand2.get()) + " = " + 
                   std::to_string(result.get());
        });
        
        // 添加观察者
        auto calc_observer = observe(expression, [this]() {
            std::cout << "Calculation: " << expression.get() << std::endl;
        });
    }
};

/// @brief 购物车示例
class ShoppingCart {
public:
    struct Item {
        string name;
        f64 price;
        i32 quantity;
        
        f64 total() const { return price * quantity; }
    };
    
    Property<vector<Item>> items{"items"};
    Property<f64> subtotal{"subtotal"};
    Property<f64> tax_rate{"tax_rate", 0.08}; // 8% tax
    Property<f64> tax_amount{"tax_amount"};
    Property<f64> total{"total"};
    
    ShoppingCart() {
        // 设置计算绑定
        bind_to(subtotal, [this]() {
            f64 sum = 0.0;
            for (const auto& item : items.get()) {
                sum += item.total();
            }
            return sum;
        });
        
        bind_to(tax_amount, [this]() {
            return subtotal.get() * tax_rate.get();
        });
        
        bind_to(total, [this]() {
            return subtotal.get() + tax_amount.get();
        });
        
        // 添加观察者
        auto cart_observer = observe(total, [this]() {
            print_receipt();
        });
    }
    
    void add_item(string_view name, f64 price, i32 quantity = 1) {
        auto current_items = items.get();
        current_items.push_back({string(name), price, quantity});
        items = std::move(current_items);
    }
    
    void print_receipt() const {
        std::cout << "\n=== Shopping Cart ===" << std::endl;
        for (const auto& item : items.get()) {
            std::cout << item.name << " x" << item.quantity 
                      << " @ $" << std::fixed << std::setprecision(2) << item.price
                      << " = $" << item.total() << std::endl;
        }
        std::cout << "Subtotal: $" << subtotal.get() << std::endl;
        std::cout << "Tax (" << (tax_rate.get() * 100) << "%): $" << tax_amount.get() << std::endl;
        std::cout << "Total: $" << total.get() << std::endl;
        std::cout << "===================" << std::endl;
    }
};

/// @brief 主函数
int main() {
    std::cout << "=== Property System Examples ===" << std::endl;
    std::cout << "Version: " << PROPERTY_VERSION << std::endl;
    std::cout << std::endl;
    
    // 人员示例
    std::cout << "1. Person Example:" << std::endl;
    Person person("John", "Doe", 25);
    std::cout << "Initial: " << person.full_name.get() << ", Age: " << person.age.get() 
              << ", Adult: " << (person.is_adult.get() ? "Yes" : "No") << std::endl;
    
    person.first_name = "Jane";
    person.age = 17;
    std::cout << std::endl;
    
    // 温度转换器示例
    std::cout << "2. Temperature Converter Example:" << std::endl;
    TemperatureConverter temp_converter;
    temp_converter.celsius = 0.0;   // 冰点
    temp_converter.celsius = 100.0; // 沸点
    temp_converter.celsius = 37.0;  // 体温
    std::cout << std::endl;
    
    // 计算器示例
    std::cout << "3. Calculator Example:" << std::endl;
    Calculator calc;
    calc.operand1 = 10.0;
    calc.operand2 = 5.0;
    calc.operation = "+";
    
    calc.operation = "*";
    calc.operand2 = 3.0;
    std::cout << std::endl;
    
    // 购物车示例
    std::cout << "4. Shopping Cart Example:" << std::endl;
    ShoppingCart cart;
    cart.add_item("Apple", 1.50, 3);
    cart.add_item("Banana", 0.75, 6);
    cart.add_item("Orange", 2.00, 2);
    
    // 修改税率
    cart.tax_rate = 0.10; // 改为10%
    std::cout << std::endl;
    
    // 高级功能示例
    std::cout << "5. Advanced Features Example:" << std::endl;
    
    // 属性验证
    auto validated_prop = make_property<i32>("score", 85);
    auto validator = make_validator(validated_prop, [](i32 value) {
        return value >= 0 && value <= 100;
    }, "Score must be between 0 and 100");
    
    validator.set_error_handler([](i32 invalid_value, string_view msg) {
        std::cout << "Validation error: " << msg << " (tried to set: " << invalid_value << ")" << std::endl;
    });
    
    std::cout << "Valid score: " << validated_prop.get() << std::endl;
    validated_prop = 95;  // Valid
    std::cout << "Updated score: " << validated_prop.get() << std::endl;
    validated_prop = 150; // Invalid
    std::cout << "Score after invalid update: " << validated_prop.get() << std::endl;
    
    std::cout << "\n=== Examples Complete ===" << std::endl;
    return 0;
}
