/**
 * @file bindable_property.hpp
 * @brief 简化的可绑定属性实现
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "simple_binding.hpp"

namespace property::binding {

/// @brief 可绑定属性
template<typename T>
class BindableProperty : public PropertyBase<T>, public INotifiable {
    static_assert(PropertyValue<T>, "T must be a valid property value type");
public:
    using binding_ptr = std::shared_ptr<IBinding<T>>;
    
    /// @brief 构造函数
    explicit BindableProperty(
        string_view name = {},
        const T& initial_value = T{}
    ) : PropertyBase<T>(name, initial_value) {}
    
    /// @brief 是否有绑定
    [[nodiscard]] bool has_binding() const noexcept override {
        std::lock_guard lock(mutex_);
        return binding_ != nullptr;
    }
    
    /// @brief 设置绑定
    template<typename F>
    void set_binding(F&& func, const SourceLocation& loc = {}) {
        static_assert(BindingFunction<F, T>, "F must be a valid binding function");

        auto new_binding = std::make_shared<FunctionBinding<T, std::decay_t<F>>>(
            std::forward<F>(func), loc
        );

        std::lock_guard lock(mutex_);
        
        // 清除旧的依赖关系
        clear_dependencies();
        
        binding_ = std::move(new_binding);

        // 立即求值并建立依赖关系
        try {
            this->value_ = binding_->evaluate();
            establish_dependencies();
        } catch (...) {
            // 绑定求值失败，保持原值
        }
    }
    
    /// @brief 清除绑定
    void clear_binding() override {
        clear_dependencies();
        std::lock_guard lock(mutex_);
        binding_.reset();
    }
    
    /// @brief 获取绑定
    [[nodiscard]] binding_ptr get_binding() const {
        std::lock_guard lock(mutex_);
        return binding_;
    }
    
    /// @brief 赋值运算符
    BindableProperty& operator=(const T& value) {
        this->set(value);
        return *this;
    }
    
    BindableProperty& operator=(T&& value) {
        this->set(std::move(value));
        return *this;
    }
    
    /// @brief 获取属性的shared_ptr（用于依赖追踪）
    std::weak_ptr<IProperty> get_shared_ptr() const override {
        // 简化实现：返回空的weak_ptr，使用裸指针进行依赖管理
        return std::weak_ptr<IProperty>{};
    }

    /// @brief 获取值（重写以支持依赖追踪）
    [[nodiscard]] const T& get() const noexcept {
        evaluate_binding_if_needed();

        // 如果正在进行依赖收集，将此属性添加到依赖列表
        auto& tracker = DependencyTracker::current();
        if (tracker.is_collecting()) {
            // 使用裸指针进行依赖追踪
            tracker.add_dependency_raw(static_cast<const IProperty*>(this));
        }

        return this->value_;
    }

protected:
    /// @brief 如果需要则求值绑定
    void evaluate_binding_if_needed() const override {
        std::lock_guard lock(mutex_);
        if (binding_) {
            try {
                this->value_ = binding_->evaluate();
            } catch (...) {
                // 绑定求值失败，保持原值
            }
        }
    }

    /// @brief 通知依赖属性
    void notify_dependents() override {
        DependencyManager::instance().notify_dependents(static_cast<IProperty*>(this));
    }

    /// @brief 依赖变化时的回调（INotifiable接口）
    void on_dependency_changed() override {
        std::lock_guard lock(mutex_);
        if (binding_) {
            binding_->mark_dirty();
        }
    }

private:
    /// @brief 建立依赖关系
    void establish_dependencies() {
        if (!binding_) return;
        
        auto dependencies = binding_->dependencies();
        for (const auto& weak_dep : dependencies) {
            if (auto dep = weak_dep.lock()) {
                DependencyManager::instance().register_dependency(
                    dep.get(), 
                    get_shared_ptr()
                );
            }
        }
    }
    
    /// @brief 清除依赖关系
    void clear_dependencies() {
        DependencyManager::instance().clear_dependencies(static_cast<IProperty*>(this));
    }
    
    mutable std::mutex mutex_;
    binding_ptr binding_;
};

/// @brief 创建绑定函数
template<typename F>
[[nodiscard]] auto make_binding(F&& func, const SourceLocation& loc = {}) {
    using ReturnType = std::invoke_result_t<F>;
    return std::make_shared<FunctionBinding<ReturnType, std::decay_t<F>>>(
        std::forward<F>(func), loc
    );
}

/// @brief 绑定工厂函数
template<typename T, typename F>
[[nodiscard]] auto bind(F&& func, const SourceLocation& loc = {}) {
    return [func = std::forward<F>(func), loc](auto& property) {
        property.set_binding(func, loc);
    };
}

/// @brief 绑定操作符
template<typename T, typename F>
BindableProperty<T>& operator<<=(BindableProperty<T>& property, F&& func) {
    static_assert(BindingFunction<F, T>, "F must be a valid binding function for type T");
    property.set_binding(std::forward<F>(func));
    return property;
}

} // namespace property::binding
