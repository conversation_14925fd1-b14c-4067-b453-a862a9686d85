/****************************************************************************
**
** Simplified Property implementation based on Qt's QProperty
**
****************************************************************************/

#ifndef PROPERTY_H
#define PROPERTY_H

#include <type_traits>
#include <memory>
#include <string>
#include <vector>
#include <unordered_set>
#include <iostream>

// Forward declarations
template <typename T> class PropertyBinding;
class PropertyObserver;
class UntypedPropertyData;
class UntypedPropertyBinding;

namespace PropertyPrivate {
class PropertyBindingData;
class BindingEvaluationState;
}

// Global binding evaluation state
namespace PropertyPrivate {
// Current binding being evaluated
extern thread_local BindingEvaluationState* currentlyEvaluatingBinding;

// Binding evaluation state
class BindingEvaluationState {
public:
    BindingEvaluationState(void* binding)
        : binding(binding), previousState(currentlyEvaluatingBinding) {
        currentlyEvaluatingBinding = this;
    }

    ~BindingEvaluationState() {
        currentlyEvaluatingBinding = previousState;
    }

    void* binding;
    BindingEvaluationState* previousState;
    std::unordered_set<const PropertyBindingData*> dependencies;
};

// Binding function interface
template <typename T>
class BindingFunction {
public:
    virtual ~BindingFunction() = default;
    virtual T evaluate() = 0;
    virtual BindingFunction<T>* clone() const = 0;
};

// Concrete binding function implementation
template <typename T, typename Functor>
class ConcreteFunctionBinding : public BindingFunction<T> {
public:
    ConcreteFunctionBinding(Functor f) : m_function(std::move(f)) {}

    // Copy constructor to ensure proper copying of the functor
    ConcreteFunctionBinding(const ConcreteFunctionBinding& other) : m_function(other.m_function) {}

    T evaluate() override {
        return m_function();
    }

    BindingFunction<T>* clone() const override {
        return new ConcreteFunctionBinding(*this);
    }

private:
    Functor m_function;
};

// Binding storage
template <typename T>
class BindingStorage {
public:
    BindingStorage() = default;
    ~BindingStorage() { delete function; }

    BindingStorage(const BindingStorage& other)
        : function(other.function ? other.function->clone() : nullptr),
        dirty(true) {}

    BindingStorage& operator=(const BindingStorage& other) {
        if (this != &other) {
            delete function;
            function = other.function ? other.function->clone() : nullptr;
            dirty = true;
        }
        return *this;
    }

    BindingStorage(BindingStorage&& other) noexcept
        : function(other.function), dirty(other.dirty) {
        other.function = nullptr;
    }

    BindingStorage& operator=(BindingStorage&& other) noexcept {
        if (this != &other) {
            delete function;
            function = other.function;
            dirty = other.dirty;
            other.function = nullptr;
        }
        return *this;
    }

    BindingFunction<T>* function = nullptr;
    bool dirty = true;
    std::vector<PropertyObserver*> observers;
};

// Binding data storage
class PropertyBindingData {
public:
    PropertyBindingData() = default;
    ~PropertyBindingData() {
        // In a real implementation, we would clean up observers
        // For simplicity, we'll just clear the vector
        observers.clear();
    }

    bool hasBinding() const { return binding_ptr != nullptr; }

    void removeBinding() {
        if (binding_ptr) {
            // Clean up binding
            binding_ptr = nullptr;
        }
    }

    void notifyObservers(UntypedPropertyData* propertyDataPtr) const;

    void evaluateIfDirty(const UntypedPropertyData* property) const;

    void registerWithCurrentlyEvaluatingBinding() const {
        if (currentlyEvaluatingBinding) {
            currentlyEvaluatingBinding->dependencies.insert(this);
        }
    }

    template<typename T>
    PropertyBinding<T> setBinding(const PropertyBinding<T>& newBinding,
                                  UntypedPropertyData* propertyDataPtr);

    void* binding_ptr = nullptr;
    mutable std::vector<PropertyObserver*> observers;
};

// Tagged pointer helper
template <typename T, typename Tag>
class TaggedPointer {
public:
    constexpr TaggedPointer() = default;

    TaggedPointer(T* ptr, Tag tag = {}) {
        set(ptr, tag);
    }

    void set(T* ptr, Tag tag = {}) {
        // Simple implementation, not bit-manipulating like Qt's version
        pointer = ptr;
        tag_value = tag;
    }

    T* get() const { return pointer; }
    Tag tag() const { return tag_value; }

    operator bool() const { return pointer != nullptr; }
    T* operator->() const { return pointer; }

private:
    T* pointer = nullptr;
    Tag tag_value = {};
};

} // namespace PropertyPrivate

// Base class for all property data
class UntypedPropertyData {
public:
    // Marker for SFINAE
    struct InheritsUntypedPropertyData {};
};

// Property data storage
template <typename T>
class PropertyData : public UntypedPropertyData {
protected:
    mutable T val = T();
private:
    class DisableRValueRefs {};
protected:
    static constexpr bool UseReferences = !(std::is_arithmetic_v<T> || std::is_enum_v<T> || std::is_pointer_v<T>);
public:
    using value_type = T;
    using parameter_type = std::conditional_t<UseReferences, const T&, T>;
    using rvalue_ref = typename std::conditional_t<UseReferences, T&&, DisableRValueRefs>;
    using arrow_operator_result = std::conditional_t<std::is_pointer_v<T>, const T&,
                                                     std::conditional_t<std::is_class_v<T> && !std::is_same_v<T, std::string>, const T&, void>>;

    PropertyData() = default;
    PropertyData(parameter_type t) : val(t) {}
    PropertyData(rvalue_ref t) : val(std::move(t)) {}
    ~PropertyData() = default;

    parameter_type valueBypassingBindings() const { return val; }
    void setValueBypassingBindings(parameter_type v) { val = v; }
    void setValueBypassingBindings(rvalue_ref v) { val = std::move(v); }
};

// Simplified binding error reporting
class PropertyBindingError {
public:
    PropertyBindingError() = default;

    bool hasError() const { return false; }
};

// Base class for property bindings
class UntypedPropertyBinding {
public:
    UntypedPropertyBinding() = default;
    UntypedPropertyBinding(UntypedPropertyBinding&& other) = default;
    UntypedPropertyBinding(const UntypedPropertyBinding& other) = default;
    UntypedPropertyBinding& operator=(const UntypedPropertyBinding& other) = default;
    UntypedPropertyBinding& operator=(UntypedPropertyBinding&& other) = default;
    ~UntypedPropertyBinding() = default;

    bool isNull() const { return !d; }
    PropertyBindingError error() const { return {}; }

protected:
    std::shared_ptr<void> d;

    // Make PropertyBinding and PropertyBindingData friends so they can access d
    template <typename T>
    friend class PropertyBinding;
    friend class PropertyPrivate::PropertyBindingData;
};

// Typed property binding
template <typename PropertyType>
class PropertyBinding : public UntypedPropertyBinding {
public:
    PropertyBinding() = default;

    template<typename Functor>
    PropertyBinding(Functor f)
        : UntypedPropertyBinding()
    {
        // Create a binding function and store it
        using BindingType = PropertyPrivate::ConcreteFunctionBinding<PropertyType, Functor>;
        auto* binding = new PropertyPrivate::BindingStorage<PropertyType>();
        binding->function = new BindingType(std::move(f));
        binding->dirty = true;
        d = std::shared_ptr<void>(binding, [](void* ptr) {
            delete static_cast<PropertyPrivate::BindingStorage<PropertyType>*>(ptr);
        });
    }

    // Internal
    explicit PropertyBinding(const UntypedPropertyBinding& binding)
        : UntypedPropertyBinding(binding)
    {}
};

// Helper to create property bindings
namespace PropertyUtil {
template <typename Functor>
auto makeBinding(Functor&& f, std::enable_if_t<std::is_invocable_v<Functor>>* = nullptr)
{
    return PropertyBinding<std::invoke_result_t<Functor>>(std::forward<Functor>(f));
}
}

// Observer tag enum
enum ObserverTag {
    ObserverNotifiesBinding,
    ObserverNotifiesChangeHandler,
    ObserverIsPlaceholder
};

// Base class for property observers
class PropertyObserverBase {
public:
    using ChangeHandler = void (*)(PropertyObserver*, UntypedPropertyData*);

    friend class PropertyPrivate::PropertyBindingData;

protected:
    PropertyPrivate::TaggedPointer<PropertyObserver, ObserverTag> next;
    void* prev = nullptr;

    union {
        void* bindingToMarkDirty = nullptr;
        ChangeHandler changeHandler;
        UntypedPropertyData* aliasedPropertyData;
    };
};

// Property observer class
class PropertyObserver : public PropertyObserverBase {
public:
    constexpr PropertyObserver() = default;
    PropertyObserver(PropertyObserver&& other) noexcept = default;
    PropertyObserver& operator=(PropertyObserver&& other) noexcept = default;
    ~PropertyObserver() = default;

    template<typename Property, typename = typename Property::InheritsUntypedPropertyData>
    void setSource(const Property& property)
    { setSource(property.bindingData()); }

    void setSource(const PropertyPrivate::PropertyBindingData& property) {
        // Register this observer with the property
        const_cast<PropertyPrivate::PropertyBindingData&>(property).observers.push_back(this);
    }

protected:
    PropertyObserver(ChangeHandler changeHandler) : PropertyObserverBase() {
        this->changeHandler = changeHandler;
    }

private:
    PropertyObserver(const PropertyObserver&) = delete;
    PropertyObserver& operator=(const PropertyObserver&) = delete;
};

// Property change handler
template <typename Functor>
class PropertyChangeHandler : public PropertyObserver {
    Functor m_handler;
public:
    PropertyChangeHandler(Functor handler)
        : PropertyObserver([](PropertyObserver* self, UntypedPropertyData*) {
            auto This = static_cast<PropertyChangeHandler<Functor>*>(self);
            This->m_handler();
        })
        , m_handler(handler)
    {
    }

    template<typename Property, typename = typename Property::InheritsUntypedPropertyData>
    PropertyChangeHandler(const Property& property, Functor handler)
        : PropertyObserver([](PropertyObserver* self, UntypedPropertyData*) {
            auto This = static_cast<PropertyChangeHandler<Functor>*>(self);
            This->m_handler();
        })
        , m_handler(handler)
    {
        setSource(property);
    }
};

// Main Property class
template <typename T>
class Property : public PropertyData<T> {
    PropertyPrivate::PropertyBindingData d;
    bool is_equal(const T& v) {
        if constexpr (std::is_same_v<T, T>) { // Simplified check for equality operator
            if (v == this->val)
                return true;
        }
        return false;
    }

public:
    using value_type = typename PropertyData<T>::value_type;
    using parameter_type = typename PropertyData<T>::parameter_type;
    using rvalue_ref = typename PropertyData<T>::rvalue_ref;
    using arrow_operator_result = typename PropertyData<T>::arrow_operator_result;
    using InheritsUntypedPropertyData = typename UntypedPropertyData::InheritsUntypedPropertyData;

    Property() = default;
    explicit Property(parameter_type initialValue) : PropertyData<T>(initialValue) {}
    explicit Property(rvalue_ref initialValue) : PropertyData<T>(std::move(initialValue)) {}
    explicit Property(const PropertyBinding<T>& binding)
        : Property()
    { setBinding(binding); }

    template <typename Functor>
    explicit Property(Functor&& f,
                      typename std::enable_if_t<std::is_invocable_r_v<T, Functor&>>* = nullptr)
        : Property(PropertyBinding<T>(std::forward<Functor>(f)))
    {}

    ~Property() = default;

    parameter_type value() const {
        if (d.hasBinding()) {
            // Always evaluate the binding
            auto* storage = static_cast<PropertyPrivate::BindingStorage<T>*>(d.binding_ptr);
            if (storage->function) {
                // Set up binding evaluation state to track dependencies
                PropertyPrivate::BindingEvaluationState evaluationState(storage);

                // Evaluate the binding
                this->val = storage->function->evaluate();

                // Mark binding as clean
                storage->dirty = false;
            }
        }
        d.registerWithCurrentlyEvaluatingBinding();
        return this->val;
    }

    arrow_operator_result operator->() const {
        if constexpr (std::is_class_v<T> && !std::is_same_v<T, std::string>) {
            return value();
        } else if constexpr (std::is_pointer_v<T>) {
            value();
            return this->val;
        } else {
            return;
        }
    }

    parameter_type operator*() const {
        return value();
    }

    operator parameter_type() const {
        return value();
    }

    void setValue(rvalue_ref newValue) {
        d.removeBinding();
        if (is_equal(newValue))
            return;
        this->val = std::move(newValue);

        // For simplicity in this example, we'll just re-evaluate all bindings
        // In a real implementation, we would track dependencies more precisely
        // std::cout << "Property value changed to: " << this->val << std::endl;

        notify();
    }

    void setValue(parameter_type newValue) {
        d.removeBinding();
        if (is_equal(newValue))
            return;
        this->val = newValue;

        // For simplicity in this example, we'll just re-evaluate all bindings
        // In a real implementation, we would track dependencies more precisely
        // std::cout << "Property value changed to: " << this->val << std::endl;

        notify();
    }

    Property<T>& operator=(rvalue_ref newValue) {
        setValue(std::move(newValue));
        return *this;
    }

    Property<T>& operator=(parameter_type newValue) {
        setValue(newValue);
        return *this;
    }

    PropertyBinding<T> setBinding(const PropertyBinding<T>& newBinding) {
        PropertyBinding<T> oldBinding = d.setBinding(newBinding, this);
        notify();
        return oldBinding;
    }

    bool setBinding(const UntypedPropertyBinding& newBinding) {
        if (!newBinding.isNull()) {
            // Type checking would go here in a real implementation
            return false;
        }
        setBinding(static_cast<const PropertyBinding<T>&>(newBinding));
        return true;
    }

    template <typename Functor>
    PropertyBinding<T> setBinding(Functor&& f,
                                  std::enable_if_t<std::is_invocable_v<Functor>>* = nullptr) {
        return setBinding(PropertyUtil::makeBinding(std::forward<Functor>(f)));
    }

    bool hasBinding() const { return d.hasBinding(); }

    PropertyBinding<T> binding() const {
        // Implementation would return the current binding
        return PropertyBinding<T>();
    }

    PropertyBinding<T> takeBinding() {
        return setBinding(PropertyBinding<T>());
    }

    template<typename Functor>
    PropertyChangeHandler<Functor> onValueChanged(Functor f) {
        static_assert(std::is_invocable_v<Functor>, "Functor callback must be callable without any parameters");
        return PropertyChangeHandler<Functor>(*this, f);
    }

    template<typename Functor>
    PropertyChangeHandler<Functor> subscribe(Functor f) {
        static_assert(std::is_invocable_v<Functor>, "Functor callback must be callable without any parameters");
        f();
        return onValueChanged(f);
    }

    const PropertyPrivate::PropertyBindingData& bindingData() const { return d; }

private:
    void notify() {
        d.notifyObservers(this);
    }

    Property(const Property&) = delete;
    Property(Property&&) = delete;
    Property& operator=(const Property&) = delete;
    Property& operator=(Property&&) = delete;
};

namespace PropertyUtil {
template <typename PropertyType>
PropertyBinding<PropertyType> makeBinding(const Property<PropertyType>& otherProperty) {
    return PropertyUtil::makeBinding([&otherProperty]() -> PropertyType { return otherProperty; });
}
}

// Implementation of PropertyBindingData methods
namespace PropertyPrivate {

// Declare the thread_local variable
extern thread_local BindingEvaluationState* currentlyEvaluatingBinding;

template<typename T>
PropertyBinding<T> PropertyBindingData::setBinding(const PropertyBinding<T>& newBinding, UntypedPropertyData* propertyDataPtr) {
    PropertyBinding<T> oldBinding;

    if (hasBinding()) {
        // Store old binding before removing it
        oldBinding = PropertyBinding<T>(UntypedPropertyBinding());
        oldBinding.d = std::shared_ptr<void>(binding_ptr, [](void*) {});

        // Clean up old binding
        binding_ptr = nullptr;
    }

    if (!newBinding.isNull()) {
        // Store new binding - access d through friendship
        auto* storage = static_cast<BindingStorage<T>*>(newBinding.d.get());

        // Create a new binding storage that we own
        auto* newStorage = new BindingStorage<T>(*storage);
        binding_ptr = newStorage;

        // Mark the binding as dirty so it will be evaluated on next access
        newStorage->dirty = true;

        // For the example, let's evaluate the binding immediately
        // In a real implementation, we would defer this until the value is needed
        if (newStorage->function) {
            // Set up binding evaluation state to track dependencies
            BindingEvaluationState evaluationState(newStorage);

            // Evaluate the binding
            auto* propertyData = static_cast<PropertyData<T>*>(propertyDataPtr);
            propertyData->setValueBypassingBindings(newStorage->function->evaluate());

            // Mark binding as clean
            newStorage->dirty = false;
        }
    }

    return oldBinding;
}

inline void PropertyBindingData::notifyObservers(UntypedPropertyData* propertyDataPtr) const {
    // Notify all observers that the property has changed
    for (auto* observer : observers) {
        if (observer->changeHandler) {
            // Call the change handler
            observer->changeHandler(observer, propertyDataPtr);
        }
    }

    // Mark all dependent bindings as dirty
    // In a real implementation, we would track dependencies more precisely
    // For simplicity, we'll just mark all bindings as dirty
    if (PropertyPrivate::currentlyEvaluatingBinding) {
        // If we're currently evaluating a binding, mark it as dirty
        auto* state = PropertyPrivate::currentlyEvaluatingBinding;
        if (state->binding) {
            // Mark the binding as dirty
            for (const auto* dep : state->dependencies) {
                if (dep->binding_ptr) {
                    // Mark all bindings that depend on this property as dirty
                    // This is a simplified implementation
                    // In a real implementation, we would use type erasure
                    // For now, we'll just print a message
                    std::cout << "Marking dependent binding as dirty" << std::endl;
                }
            }
        }
    }
}

inline void PropertyBindingData::evaluateIfDirty(const UntypedPropertyData* property) const {
    // This is now handled directly in the Property<T>::value() method
    // We keep this method for API compatibility
}

} // namespace PropertyPrivate

#endif // PROPERTY_H
