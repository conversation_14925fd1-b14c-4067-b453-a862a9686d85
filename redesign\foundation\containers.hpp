/**
 * @file containers.hpp
 * @brief 现代化容器实现
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "types.hpp"
#include "utility.hpp"
#include <vector>
#include <array>
#include <deque>
#include <unordered_map>
#include <unordered_set>
#include <span>
#include <ranges>
#include <algorithm>

namespace property::foundation {

/// @brief 小对象优化的动态数组
template<typename T, size_type InlineCapacity = 16>
class small_vector {
public:
    using value_type = T;
    using size_type = foundation::size_type;
    using difference_type = std::ptrdiff_t;
    using reference = T&;
    using const_reference = const T&;
    using pointer = T*;
    using const_pointer = const T*;
    using iterator = T*;
    using const_iterator = const T*;
    using reverse_iterator = std::reverse_iterator<iterator>;
    using const_reverse_iterator = std::reverse_iterator<const_iterator>;
    
    /// @brief 默认构造函数
    small_vector() noexcept = default;
    
    /// @brief 从大小构造
    explicit small_vector(size_type count) {
        resize(count);
    }
    
    /// @brief 从大小和值构造
    small_vector(size_type count, const T& value) {
        assign(count, value);
    }
    
    /// @brief 从迭代器范围构造
    template<std::input_iterator InputIt>
    small_vector(InputIt first, InputIt last) {
        assign(first, last);
    }
    
    /// @brief 从初始化列表构造
    small_vector(std::initializer_list<T> init) {
        assign(init);
    }
    
    /// @brief 拷贝构造函数
    small_vector(const small_vector& other) {
        assign(other.begin(), other.end());
    }
    
    /// @brief 移动构造函数
    small_vector(small_vector&& other) noexcept {
        if (other.is_inline()) {
            // 移动内联存储的元素
            std::uninitialized_move_n(other.data(), other.size(), inline_data());
            size_ = other.size_;
            other.size_ = 0;
        } else {
            // 窃取堆存储
            heap_data_ = std::exchange(other.heap_data_, nullptr);
            capacity_ = std::exchange(other.capacity_, InlineCapacity);
            size_ = std::exchange(other.size_, 0);
        }
    }
    
    /// @brief 析构函数
    ~small_vector() {
        clear();
        if (!is_inline()) {
            std::free(heap_data_);
        }
    }
    
    /// @brief 拷贝赋值运算符
    small_vector& operator=(const small_vector& other) {
        if (this != &other) {
            assign(other.begin(), other.end());
        }
        return *this;
    }
    
    /// @brief 移动赋值运算符
    small_vector& operator=(small_vector&& other) noexcept {
        if (this != &other) {
            clear();
            if (!is_inline()) {
                std::free(heap_data_);
                capacity_ = InlineCapacity;
            }
            
            if (other.is_inline()) {
                std::uninitialized_move_n(other.data(), other.size(), inline_data());
                size_ = other.size_;
                other.size_ = 0;
            } else {
                heap_data_ = std::exchange(other.heap_data_, nullptr);
                capacity_ = std::exchange(other.capacity_, InlineCapacity);
                size_ = std::exchange(other.size_, 0);
            }
        }
        return *this;
    }
    
    /// @brief 赋值操作
    void assign(size_type count, const T& value) {
        clear();
        reserve(count);
        std::uninitialized_fill_n(data(), count, value);
        size_ = count;
    }
    
    template<std::input_iterator InputIt>
    void assign(InputIt first, InputIt last) {
        clear();
        if constexpr (std::forward_iterator<InputIt>) {
            auto count = std::distance(first, last);
            reserve(count);
            std::uninitialized_copy(first, last, data());
            size_ = count;
        } else {
            for (; first != last; ++first) {
                emplace_back(*first);
            }
        }
    }
    
    void assign(std::initializer_list<T> init) {
        assign(init.begin(), init.end());
    }
    
    /// @brief 元素访问
    [[nodiscard]] reference at(size_type pos) {
        if (pos >= size_) {
            throw std::out_of_range("small_vector::at");
        }
        return data()[pos];
    }
    
    [[nodiscard]] const_reference at(size_type pos) const {
        if (pos >= size_) {
            throw std::out_of_range("small_vector::at");
        }
        return data()[pos];
    }
    
    [[nodiscard]] reference operator[](size_type pos) noexcept {
        ASSERT(pos < size_);
        return data()[pos];
    }
    
    [[nodiscard]] const_reference operator[](size_type pos) const noexcept {
        ASSERT(pos < size_);
        return data()[pos];
    }
    
    [[nodiscard]] reference front() noexcept {
        ASSERT(!empty());
        return data()[0];
    }
    
    [[nodiscard]] const_reference front() const noexcept {
        ASSERT(!empty());
        return data()[0];
    }
    
    [[nodiscard]] reference back() noexcept {
        ASSERT(!empty());
        return data()[size_ - 1];
    }
    
    [[nodiscard]] const_reference back() const noexcept {
        ASSERT(!empty());
        return data()[size_ - 1];
    }
    
    [[nodiscard]] pointer data() noexcept {
        return is_inline() ? inline_data() : heap_data_;
    }
    
    [[nodiscard]] const_pointer data() const noexcept {
        return is_inline() ? inline_data() : heap_data_;
    }
    
    /// @brief 迭代器
    [[nodiscard]] iterator begin() noexcept { return data(); }
    [[nodiscard]] const_iterator begin() const noexcept { return data(); }
    [[nodiscard]] const_iterator cbegin() const noexcept { return data(); }
    
    [[nodiscard]] iterator end() noexcept { return data() + size_; }
    [[nodiscard]] const_iterator end() const noexcept { return data() + size_; }
    [[nodiscard]] const_iterator cend() const noexcept { return data() + size_; }
    
    [[nodiscard]] reverse_iterator rbegin() noexcept { return reverse_iterator(end()); }
    [[nodiscard]] const_reverse_iterator rbegin() const noexcept { return const_reverse_iterator(end()); }
    [[nodiscard]] const_reverse_iterator crbegin() const noexcept { return const_reverse_iterator(end()); }
    
    [[nodiscard]] reverse_iterator rend() noexcept { return reverse_iterator(begin()); }
    [[nodiscard]] const_reverse_iterator rend() const noexcept { return const_reverse_iterator(begin()); }
    [[nodiscard]] const_reverse_iterator crend() const noexcept { return const_reverse_iterator(begin()); }
    
    /// @brief 容量
    [[nodiscard]] bool empty() const noexcept { return size_ == 0; }
    [[nodiscard]] size_type size() const noexcept { return size_; }
    [[nodiscard]] size_type max_size() const noexcept { return std::numeric_limits<size_type>::max(); }
    [[nodiscard]] size_type capacity() const noexcept { return capacity_; }
    
    void reserve(size_type new_cap) {
        if (new_cap <= capacity_) {
            return;
        }
        
        auto new_data = static_cast<T*>(std::malloc(new_cap * sizeof(T)));
        if (!new_data) {
            throw std::bad_alloc{};
        }
        
        if constexpr (std::is_nothrow_move_constructible_v<T>) {
            std::uninitialized_move_n(data(), size_, new_data);
        } else {
            try {
                std::uninitialized_copy_n(data(), size_, new_data);
            } catch (...) {
                std::free(new_data);
                throw;
            }
        }
        
        std::destroy_n(data(), size_);
        if (!is_inline()) {
            std::free(heap_data_);
        }
        
        heap_data_ = new_data;
        capacity_ = new_cap;
    }
    
    void shrink_to_fit() {
        if (size_ <= InlineCapacity && !is_inline()) {
            // 移回内联存储
            std::uninitialized_move_n(heap_data_, size_, inline_data());
            std::destroy_n(heap_data_, size_);
            std::free(heap_data_);
            capacity_ = InlineCapacity;
        } else if (!is_inline() && capacity_ > size_) {
            // 缩小堆存储
            auto new_data = static_cast<T*>(std::malloc(size_ * sizeof(T)));
            if (new_data) {
                std::uninitialized_move_n(heap_data_, size_, new_data);
                std::destroy_n(heap_data_, size_);
                std::free(heap_data_);
                heap_data_ = new_data;
                capacity_ = size_;
            }
        }
    }
    
    /// @brief 修改器
    void clear() noexcept {
        std::destroy_n(data(), size_);
        size_ = 0;
    }
    
    template<typename... Args>
    reference emplace_back(Args&&... args) {
        if (size_ == capacity_) {
            reserve(capacity_ * 2);
        }
        auto& result = *std::construct_at(data() + size_, std::forward<Args>(args)...);
        ++size_;
        return result;
    }
    
    void push_back(const T& value) {
        emplace_back(value);
    }
    
    void push_back(T&& value) {
        emplace_back(std::move(value));
    }
    
    void pop_back() noexcept {
        ASSERT(!empty());
        std::destroy_at(data() + size_ - 1);
        --size_;
    }
    
    void resize(size_type count) {
        if (count < size_) {
            std::destroy_n(data() + count, size_ - count);
        } else if (count > size_) {
            reserve(count);
            std::uninitialized_default_construct_n(data() + size_, count - size_);
        }
        size_ = count;
    }
    
    void resize(size_type count, const T& value) {
        if (count < size_) {
            std::destroy_n(data() + count, size_ - count);
        } else if (count > size_) {
            reserve(count);
            std::uninitialized_fill_n(data() + size_, count - size_, value);
        }
        size_ = count;
    }

private:
    [[nodiscard]] bool is_inline() const noexcept {
        return capacity_ == InlineCapacity;
    }
    
    [[nodiscard]] T* inline_data() noexcept {
        return reinterpret_cast<T*>(inline_storage_.data());
    }
    
    [[nodiscard]] const T* inline_data() const noexcept {
        return reinterpret_cast<const T*>(inline_storage_.data());
    }
    
    union {
        std::array<std::aligned_storage_t<sizeof(T), alignof(T)>, InlineCapacity> inline_storage_;
        T* heap_data_;
    };
    
    size_type capacity_ = InlineCapacity;
    size_type size_ = 0;
};

/// @brief 类型别名
template<typename T>
using vector = std::vector<T>;

template<typename T>
using deque = std::deque<T>;

template<typename Key, typename Value, typename Hash = std::hash<Key>>
using hash_map = std::unordered_map<Key, Value, Hash>;

template<typename Key, typename Hash = std::hash<Key>>
using hash_set = std::unordered_set<Key, Hash>;

template<typename T, size_type Extent = std::dynamic_extent>
using span = std::span<T, Extent>;

} // namespace property::foundation
