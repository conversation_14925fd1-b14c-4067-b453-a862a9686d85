/**
 * @file utility.hpp
 * @brief 现代化C++工具函数和宏定义
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "types.hpp"
#include <utility>
#include <memory>
#include <functional>
#include <source_location>
#include <format>
#include <ranges>
#include <algorithm>

namespace property::foundation {

/// @brief 禁用拷贝和移动的宏
#define DISABLE_COPY(Class) \
    Class(const Class&) = delete; \
    Class& operator=(const Class&) = delete;

#define DISABLE_MOVE(Class) \
    Class(Class&&) = delete; \
    Class& operator=(Class&&) = delete;

#define DISABLE_COPY_MOVE(Class) \
    DISABLE_COPY(Class) \
    DISABLE_MOVE(Class)

/// @brief 默认拷贝和移动的宏
#define DEFAULT_COPY_MOVE(Class) \
    Class(const Class&) = default; \
    Class& operator=(const Class&) = default; \
    Class(Class&&) = default; \
    Class& operator=(Class&&) = default;

/// @brief 强制内联宏
#if defined(__GNUC__) || defined(__clang__)
    #define FORCE_INLINE inline __attribute__((always_inline))
#elif defined(_MSC_VER)
    #define FORCE_INLINE inline __forceinline
#else
    #define FORCE_INLINE inline
#endif

/// @brief 可能未使用的变量标记
#define MAYBE_UNUSED [[maybe_unused]]

/// @brief 预期分支提示
#define LIKELY [[likely]]
#define UNLIKELY [[unlikely]]

/// @brief 调试断言
#ifdef NDEBUG
    #define ASSERT(condition) ((void)0)
    #define ASSERT_MSG(condition, message) ((void)0)
#else
    #define ASSERT(condition) \
        do { \
            if (!(condition)) UNLIKELY { \
                std::terminate(); \
            } \
        } while(0)
    
    #define ASSERT_MSG(condition, message) \
        do { \
            if (!(condition)) UNLIKELY { \
                std::terminate(); \
            } \
        } while(0)
#endif

/// @brief 源代码位置信息
struct SourceLocation {
    const char* file_name;
    const char* function_name;
    u32 line;
    u32 column;
    
    constexpr SourceLocation(
        const char* file = __builtin_FILE(),
        const char* function = __builtin_FUNCTION(),
        u32 line = __builtin_LINE(),
        u32 column = 0
    ) noexcept 
        : file_name(file), function_name(function), line(line), column(column) {}
    
    #ifdef __cpp_lib_source_location
    constexpr SourceLocation(const std::source_location& loc) noexcept
        : file_name(loc.file_name())
        , function_name(loc.function_name())
        , line(loc.line())
        , column(loc.column()) {}
    #endif
    
    [[nodiscard]] string to_string() const {
        return std::format("{}:{}:{} in {}", file_name, line, column, function_name);
    }
};

/// @brief 智能指针工具函数
template<typename T, typename... Args>
[[nodiscard]] constexpr auto make_unique(Args&&... args) {
    return std::make_unique<T>(std::forward<Args>(args)...);
}

template<typename T, typename... Args>
[[nodiscard]] constexpr auto make_shared(Args&&... args) {
    return std::make_shared<T>(std::forward<Args>(args)...);
}

/// @brief 类型擦除包装器
template<typename Signature>
class function_ref; // 前向声明

template<typename R, typename... Args>
class function_ref<R(Args...)> {
public:
    template<typename F>
        requires std::invocable<F&, Args...> && 
                 std::convertible_to<std::invoke_result_t<F&, Args...>, R>
    constexpr function_ref(F&& f) noexcept
        : object_(std::addressof(f))
        , invoker_([](void* obj, Args... args) -> R {
            return std::invoke(*static_cast<std::add_pointer_t<F>>(obj), 
                             std::forward<Args>(args)...);
          }) {}
    
    constexpr R operator()(Args... args) const {
        return invoker_(object_, std::forward<Args>(args)...);
    }

private:
    void* object_;
    R (*invoker_)(void*, Args...);
};

/// @brief 范围工具函数
template<std::ranges::range R>
[[nodiscard]] constexpr auto size(R&& r) {
    return std::ranges::size(r);
}

template<std::ranges::range R>
[[nodiscard]] constexpr bool empty(R&& r) {
    return std::ranges::empty(r);
}

template<std::ranges::range R, typename T>
[[nodiscard]] constexpr bool contains(R&& r, const T& value) {
    return std::ranges::find(r, value) != std::ranges::end(r);
}

template<std::ranges::range R, typename Pred>
[[nodiscard]] constexpr auto find_if(R&& r, Pred&& pred) {
    return std::ranges::find_if(r, std::forward<Pred>(pred));
}

/// @brief 哈希组合函数
template<typename T>
constexpr void hash_combine(size_type& seed, const T& value) {
    seed ^= std::hash<T>{}(value) + 0x9e3779b9 + (seed << 6) + (seed >> 2);
}

template<typename... Args>
[[nodiscard]] constexpr size_type hash_values(const Args&... args) {
    size_type seed = 0;
    (hash_combine(seed, args), ...);
    return seed;
}

/// @brief 作用域守卫
template<typename F>
class scope_guard {
public:
    explicit scope_guard(F&& f) : f_(std::forward<F>(f)), active_(true) {}
    
    ~scope_guard() {
        if (active_) {
            f_();
        }
    }
    
    void dismiss() noexcept { active_ = false; }
    
    DISABLE_COPY_MOVE(scope_guard)

private:
    F f_;
    bool active_;
};

template<typename F>
[[nodiscard]] auto make_scope_guard(F&& f) {
    return scope_guard<std::decay_t<F>>(std::forward<F>(f));
}

/// @brief 延迟执行
#define DEFER(code) \
    auto CONCAT(_defer_, __LINE__) = make_scope_guard([&]() { code; })

#define CONCAT_IMPL(x, y) x##y
#define CONCAT(x, y) CONCAT_IMPL(x, y)

} // namespace property::foundation
