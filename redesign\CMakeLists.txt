cmake_minimum_required(VERSION 3.20)

project(ModernPropertySystem 
    VERSION 2.0.0
    DESCRIPTION "Modern C++ Property System"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    add_compile_options(/W4 /permissive-)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 调试/发布配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_compile_definitions(DEBUG=1)
else()
    add_compile_definitions(NDEBUG=1)
    if(NOT MSVC)
        add_compile_options(-O3)
    endif()
endif()

# 头文件库
add_library(property_system INTERFACE)

target_include_directories(property_system INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include>
)

target_compile_features(property_system INTERFACE cxx_std_20)

# 安装头文件
install(DIRECTORY foundation/ DESTINATION include/property/foundation)
install(DIRECTORY memory/ DESTINATION include/property/memory)
install(DIRECTORY core/ DESTINATION include/property/core)
install(DIRECTORY binding/ DESTINATION include/property/binding)
install(DIRECTORY observer/ DESTINATION include/property/observer)
install(FILES property.hpp DESTINATION include/property)

# 测试
option(BUILD_TESTS "Build tests" ON)
if(BUILD_TESTS)
    enable_testing()
    
    add_executable(property_tests tests/property_tests.cpp)
    target_link_libraries(property_tests PRIVATE property_system)
    
    add_test(NAME PropertySystemTests COMMAND property_tests)
endif()

# 示例
option(BUILD_EXAMPLES "Build examples" ON)
if(BUILD_EXAMPLES)
    add_executable(basic_usage examples/basic_usage.cpp)
    target_link_libraries(basic_usage PRIVATE property_system)
endif()

# 包配置
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/PropertySystemConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/PropertySystemConfig.cmake"
    INSTALL_DESTINATION lib/cmake/PropertySystem
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/PropertySystemConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(TARGETS property_system
    EXPORT PropertySystemTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(EXPORT PropertySystemTargets
    FILE PropertySystemTargets.cmake
    NAMESPACE PropertySystem::
    DESTINATION lib/cmake/PropertySystem
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/PropertySystemConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/PropertySystemConfigVersion.cmake"
    DESTINATION lib/cmake/PropertySystem
)

# 文档
option(BUILD_DOCS "Build documentation" OFF)
if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# 打包
set(CPACK_PACKAGE_NAME "ModernPropertySystem")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "AI Agent")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_RESOURCE_FILE_README "${CMAKE_CURRENT_SOURCE_DIR}/README.md")

include(CPack)
