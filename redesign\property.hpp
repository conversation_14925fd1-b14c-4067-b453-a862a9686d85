/**
 * @file property.hpp
 * @brief 现代化C++属性系统 - 统一头文件
 * <AUTHOR> Agent
 * @date 2025-07-10
 * @version 2.0
 * 
 * 这是一个完全重构的现代化C++属性系统，具有以下特性：
 * - 使用C++20/23标准特性
 * - 简化的设计和清晰的API
 * - 高性能的绑定和观察者系统
 * - 类型安全和异常安全
 * - 现代化的内存管理
 */

#pragma once

// 基础设施
#include "foundation/types.hpp"
#include "foundation/utility.hpp"
#include "foundation/meta_type.hpp"
#include "foundation/containers.hpp"

// 内存管理
#include "memory/smart_pointers.hpp"

// 核心属性系统
#include "core/property_base.hpp"

// 绑定系统
#include "binding/property_binding.hpp"

// 观察者系统
#include "observer/property_observer.hpp"

// 标准库头文件
#include <iostream>
#include <cmath>

/// @brief 属性系统命名空间
namespace property {

// 导入常用类型和函数
using namespace foundation;
using namespace core;
using namespace binding;
using namespace observer;
using namespace memory;

/// @brief 版本信息
struct Version {
    static constexpr u32 major = 2;
    static constexpr u32 minor = 0;
    static constexpr u32 patch = 0;
    
    [[nodiscard]] static constexpr string_view string() noexcept {
        return "2.0.0";
    }
};

/// @brief 属性类型别名
template<typename T>
using Property = BindableProperty<T>;

template<typename T>
using ReadOnly = ReadOnlyProperty<T>;

template<typename T>
using Simple = SimpleProperty<T>;

/// @brief 创建属性的便利函数
template<typename T>
[[nodiscard]] auto make_property(
    string_view name = {},
    const T& initial_value = T{}
) {
    return Property<T>(name, initial_value);
}

template<typename T>
[[nodiscard]] auto make_readonly_property(
    string_view name = {},
    const T& initial_value = T{}
) {
    return ReadOnly<T>(name, initial_value);
}

template<typename T>
[[nodiscard]] auto make_simple_property(
    string_view name = {},
    const T& initial_value = T{}
) {
    return Simple<T>(name, initial_value);
}

/// @brief 绑定操作符和函数
template<typename T, typename F>
void bind_to(Property<T>& property, F&& func, const SourceLocation& loc = {}) {
    static_assert(BindingFunction<F, T>, "F must be a valid binding function for type T");
    property.set_binding(std::forward<F>(func), loc);
}

template<typename T, typename F>
void bind_to(ReadOnly<T>& property, F&& func, const SourceLocation& loc = {}) {
    static_assert(BindingFunction<F, T>, "F must be a valid binding function for type T");
    // ReadOnly属性需要特殊处理，暂时不支持绑定
    (void)property;
    (void)func;
    (void)loc;
}

/// @brief 观察者便利函数
template<typename T, typename F>
auto observe(Property<T>& property, F&& callback, ObserverPriority priority = ObserverPriority::Normal) {
    return make_scoped_observer(property, std::forward<F>(callback), priority);
}

/// @brief 属性组合器
template<typename... Properties>
class PropertyGroup {
public:
    explicit PropertyGroup(Properties&... props) : properties_(props...) {}
    
    /// @brief 添加观察者到所有属性
    template<typename F>
    void observe_all(F&& callback, ObserverPriority priority = ObserverPriority::Normal) {
        std::apply([&](auto&... props) {
            (props.add_observer(make_simple_observer(callback, priority)), ...);
        }, properties_);
    }
    
    /// @brief 清除所有属性的观察者
    void clear_all_observers() {
        std::apply([](auto&... props) {
            (props.clear_observers(), ...);
        }, properties_);
    }

private:
    std::tuple<Properties&...> properties_;
};

/// @brief 创建属性组
template<typename... Properties>
[[nodiscard]] auto make_property_group(Properties&... props) {
    return PropertyGroup<Properties...>(props...);
}

/// @brief 属性验证器
template<typename T>
class PropertyValidator {
public:
    using validator_func = std::function<bool(const T&)>;
    using error_handler_func = std::function<void(const T&, string_view)>;
    
    PropertyValidator(Property<T>& property, validator_func validator, string_view error_msg = "Validation failed")
        : property_(property)
        , validator_(std::move(validator))
        , error_message_(error_msg) {
        
        // 添加验证观察者
        observer_ = make_observer<T>(
            [this](const T& old_val, const T& new_val) {
                if (!validator_(new_val)) {
                    // 验证失败，恢复旧值
                    property_.set(old_val);
                    if (error_handler_) {
                        error_handler_(new_val, error_message_);
                    }
                }
            },
            ObserverPriority::Highest // 最高优先级，确保首先执行验证
        );
        
        property_.add_observer(observer_);
    }
    
    /// @brief 设置错误处理器
    void set_error_handler(error_handler_func handler) {
        error_handler_ = std::move(handler);
    }

private:
    Property<T>& property_;
    validator_func validator_;
    error_handler_func error_handler_;
    string error_message_;
    std::shared_ptr<IPropertyObserver> observer_;
};

/// @brief 创建属性验证器
template<typename T, typename F>
[[nodiscard]] auto make_validator(Property<T>& property, F&& validator, string_view error_msg = "Validation failed") {
    return PropertyValidator<T>(property, std::forward<F>(validator), error_msg);
}

/// @brief 属性转换器
template<typename From, typename To>
class PropertyConverter {
public:
    using convert_func = std::function<To(const From&)>;
    
    PropertyConverter(const Property<From>& source, Property<To>& target, convert_func converter)
        : source_(source), target_(target), converter_(std::move(converter)) {
        
        // 设置绑定
        target_.set_binding([this]() {
            return converter_(source_.get());
        });
    }

private:
    const Property<From>& source_;
    Property<To>& target_;
    convert_func converter_;
};

/// @brief 创建属性转换器
template<typename From, typename To, typename F>
[[nodiscard]] auto make_converter(const Property<From>& source, Property<To>& target, F&& converter) {
    return PropertyConverter<From, To>(source, target, std::forward<F>(converter));
}

/// @brief 属性同步器
template<typename T>
class PropertySynchronizer {
public:
    PropertySynchronizer(Property<T>& prop1, Property<T>& prop2)
        : prop1_(prop1), prop2_(prop2) {
        
        // 双向同步
        observer1_ = make_observer<T>(
            [&prop2](const T&, const T& new_val) {
                prop2.set(new_val);
            }
        );
        
        observer2_ = make_observer<T>(
            [&prop1](const T&, const T& new_val) {
                prop1.set(new_val);
            }
        );
        
        prop1_.add_observer(observer1_);
        prop2_.add_observer(observer2_);
    }

private:
    Property<T>& prop1_;
    Property<T>& prop2_;
    std::shared_ptr<IPropertyObserver> observer1_;
    std::shared_ptr<IPropertyObserver> observer2_;
};

/// @brief 创建属性同步器
template<typename T>
[[nodiscard]] auto make_synchronizer(Property<T>& prop1, Property<T>& prop2) {
    return PropertySynchronizer<T>(prop1, prop2);
}

/// @brief 常用属性类型别名
using IntProperty = Property<i32>;
using FloatProperty = Property<f32>;
using DoubleProperty = Property<f64>;
using BoolProperty = Property<bool>;
using StringProperty = Property<string>;

using ReadOnlyInt = ReadOnly<i32>;
using ReadOnlyFloat = ReadOnly<f32>;
using ReadOnlyDouble = ReadOnly<f64>;
using ReadOnlyBool = ReadOnly<bool>;
using ReadOnlyString = ReadOnly<string>;

/// @brief 属性系统初始化
class PropertySystem {
public:
    /// @brief 获取全局实例
    [[nodiscard]] static PropertySystem& instance() {
        static PropertySystem system;
        return system;
    }
    
    /// @brief 获取版本信息
    [[nodiscard]] static constexpr Version version() noexcept {
        return Version{};
    }
    
    /// @brief 获取统计信息
    struct Statistics {
        size_type total_properties = 0;
        size_type total_bindings = 0;
        size_type total_observers = 0;
    };
    
    [[nodiscard]] Statistics get_statistics() const {
        // 实际实现中应该收集真实的统计信息
        return Statistics{};
    }

private:
    PropertySystem() = default;
};

} // namespace property

/// @brief 全局便利宏
#define PROPERTY_VERSION property::Version::string()

#define DECLARE_PROPERTY(type, name) \
    property::Property<type> name{#name}

#define DECLARE_READONLY_PROPERTY(type, name) \
    property::ReadOnly<type> name{#name}

#define BIND_PROPERTY(prop, expr) \
    prop.set_binding([&]() { return expr; })

#define OBSERVE_PROPERTY(prop, callback) \
    auto CONCAT(_observer_, __LINE__) = property::observe(prop, callback)

/// @brief 使用示例和文档
namespace property::examples {

/// @brief 基本使用示例
inline void basic_usage_example() {
    // 创建属性
    auto name = make_property<string>("name", "John");
    auto age = make_property<i32>("age", 25);
    auto is_adult = make_readonly_property<bool>("is_adult");
    
    // 设置绑定
    bind_to(is_adult, [&]() { return age.get() >= 18; });
    
    // 添加观察者
    auto observer = observe(name, []() {
        std::cout << "Name changed!" << std::endl;
    });
    
    // 修改值
    name = "Jane";  // 触发观察者
    age = 30;       // 触发is_adult重新计算
}

} // namespace property::examples
