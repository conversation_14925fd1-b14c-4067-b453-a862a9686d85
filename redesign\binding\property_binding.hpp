/**
 * @file property_binding.hpp
 * @brief 现代化属性绑定系统
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "../foundation/types.hpp"
#include "../foundation/utility.hpp"
#include "../core/property_base.hpp"
#include "../memory/smart_pointers.hpp"
#include <functional>
#include <concepts>
#include <atomic>
#include <mutex>
#include <unordered_map>
#include <algorithm>

namespace property::binding {

using namespace foundation;
using namespace core;
using namespace memory;

/// @brief 可绑定属性接口
class IBindableProperty {
public:
    virtual ~IBindableProperty() = default;
    virtual void mark_binding_dirty() = 0;
};

/// @brief 绑定状态
enum class BindingState {
    Clean,      ///< 绑定是干净的，值是最新的
    Dirty,      ///< 绑定是脏的，需要重新计算
    Evaluating, ///< 正在计算中
    Error       ///< 计算出错
};

/// @brief 绑定错误信息
struct BindingError {
    string message;
    SourceLocation location;
    
    BindingError(string_view msg = {}, const SourceLocation& loc = {})
        : message(msg), location(loc) {}
};

/// @brief 绑定接口
template<typename T>
class IBinding {
public:
    virtual ~IBinding() = default;
    
    /// @brief 计算绑定值
    [[nodiscard]] virtual T evaluate() = 0;
    
    /// @brief 获取绑定状态
    [[nodiscard]] virtual BindingState state() const noexcept = 0;
    
    /// @brief 标记为脏
    virtual void mark_dirty() noexcept = 0;
    
    /// @brief 是否有错误
    [[nodiscard]] virtual bool has_error() const noexcept = 0;
    
    /// @brief 获取错误信息
    [[nodiscard]] virtual const BindingError& error() const noexcept = 0;
    
    /// @brief 获取依赖的属性
    [[nodiscard]] virtual vector<std::weak_ptr<IProperty>> dependencies() const = 0;
};

/// @brief 函数绑定实现
template<typename T, typename F>
class FunctionBinding : public IBinding<T> {
    static_assert(BindingFunction<F, T>, "F must be a valid binding function for type T");
public:
    explicit FunctionBinding(F&& func, const SourceLocation& loc = {})
        : function_(std::forward<F>(func))
        , location_(loc)
        , state_(BindingState::Dirty) {}
    
    /// @brief 计算绑定值
    [[nodiscard]] T evaluate() override {
        if (state_ == BindingState::Evaluating) {
            throw std::runtime_error("Circular dependency detected");
        }

        if (state_ == BindingState::Clean) {
            return cached_value_;
        }

        state_ = BindingState::Evaluating;

        try {
            // 开始依赖收集
            DependencyTracker::CollectionScope scope(dependencies_);

            // 执行绑定函数，此时会自动收集依赖
            cached_value_ = function_();
            error_ = BindingError{}; // 清除错误
            state_ = BindingState::Clean;

            return cached_value_;
        } catch (const std::exception& e) {
            state_ = BindingState::Error;
            error_ = BindingError{e.what(), location_};
            throw;
        }
    }
    
    /// @brief 获取绑定状态
    [[nodiscard]] BindingState state() const noexcept override {
        return state_;
    }
    
    /// @brief 标记为脏
    void mark_dirty() noexcept override {
        state_ = BindingState::Dirty;
    }
    
    /// @brief 是否有错误
    [[nodiscard]] bool has_error() const noexcept override {
        return state_ == BindingState::Error;
    }
    
    /// @brief 获取错误信息
    [[nodiscard]] const BindingError& error() const noexcept override {
        return error_;
    }
    
    /// @brief 获取依赖的属性
    [[nodiscard]] vector<std::weak_ptr<IProperty>> dependencies() const override {
        return dependencies_;
    }

private:
    F function_;
    SourceLocation location_;
    std::atomic<BindingState> state_;
    mutable T cached_value_{};
    BindingError error_;
    vector<std::weak_ptr<IProperty>> dependencies_;
};

/// @brief 可绑定属性
template<typename T>
class BindableProperty : public PropertyBase<T>, public IBindableProperty,
                        public std::enable_shared_from_this<BindableProperty<T>> {
    static_assert(PropertyValue<T>, "T must be a valid property value type");
public:
    using binding_ptr = std::shared_ptr<IBinding<T>>;
    
    /// @brief 构造函数
    explicit BindableProperty(
        string_view name = {},
        const T& initial_value = T{}
    ) : PropertyBase<T>(name, initial_value) {}

    /// @brief 拷贝构造函数
    BindableProperty(const BindableProperty& other)
        : PropertyBase<T>(other.name_, other.value_) {}

    /// @brief 移动构造函数
    BindableProperty(BindableProperty&& other) noexcept
        : PropertyBase<T>(std::move(other.name_), std::move(other.value_)) {}

    /// @brief 拷贝赋值运算符
    BindableProperty& operator=(const BindableProperty& other) {
        if (this != &other) {
            this->name_ = other.name_;
            this->set(other.get());
        }
        return *this;
    }

    /// @brief 移动赋值运算符
    BindableProperty& operator=(BindableProperty&& other) noexcept {
        if (this != &other) {
            this->name_ = std::move(other.name_);
            this->set(std::move(other.value_));
        }
        return *this;
    }
    
    /// @brief 是否有绑定
    [[nodiscard]] bool has_binding() const noexcept override {
        std::lock_guard lock(mutex_);
        return binding_ != nullptr;
    }
    
    /// @brief 设置绑定
    template<typename F>
    void set_binding(F&& func, const SourceLocation& loc = {}) {
        static_assert(BindingFunction<F, T>, "F must be a valid binding function");

        // 清除旧的依赖关系
        clear_dependencies();

        auto new_binding = std::make_shared<FunctionBinding<T, std::decay_t<F>>>(
            std::forward<F>(func), loc
        );

        std::lock_guard lock(mutex_);
        binding_ = std::move(new_binding);

        // 立即求值并建立依赖关系
        try {
            this->value_ = binding_->evaluate();
            establish_dependencies();
        } catch (...) {
            // 绑定求值失败，保持原值
        }
    }
    
    /// @brief 清除绑定
    void clear_binding() override {
        clear_dependencies();
        std::lock_guard lock(mutex_);
        binding_.reset();
    }

    /// @brief 标记绑定为脏状态（IBindableProperty接口）
    void mark_binding_dirty() override {
        std::lock_guard lock(mutex_);
        if (binding_) {
            binding_->mark_dirty();
        }
    }
    
    /// @brief 获取绑定
    [[nodiscard]] binding_ptr get_binding() const {
        std::lock_guard lock(mutex_);
        return binding_;
    }

    /// @brief 赋值运算符
    BindableProperty& operator=(const T& value) {
        this->set(value);
        return *this;
    }

    BindableProperty& operator=(T&& value) {
        this->set(std::move(value));
        return *this;
    }

protected:
    /// @brief 如果需要则求值绑定
    void evaluate_binding_if_needed() const override {
        std::lock_guard lock(mutex_);
        if (binding_) {
            try {
                this->value_ = binding_->evaluate();
            } catch (...) {
                // 绑定求值失败，保持原值
            }
        }
    }

    /// @brief 通知依赖属性
    void notify_dependents() override {
        auto it = dependency_nodes_.find(static_cast<IProperty*>(this));
        if (it != dependency_nodes_.end()) {
            it->second.notify_dependents();
        }
    }

private:
    /// @brief 建立依赖关系
    void establish_dependencies() {
        if (!binding_) return;

        auto dependencies = binding_->dependencies();
        for (const auto& weak_dep : dependencies) {
            if (auto dep = weak_dep.lock()) {
                // 将当前属性注册为依赖属性的观察者
                dependency_nodes_[dep.get()].add_dependent(
                    std::static_pointer_cast<IProperty>(this->shared_from_this())
                );
            }
        }
    }

    /// @brief 清除依赖关系
    void clear_dependencies() {
        for (auto& [prop, node] : dependency_nodes_) {
            node.remove_dependent(
                std::static_pointer_cast<IProperty>(this->shared_from_this())
            );
        }
        dependency_nodes_.clear();
    }

    mutable std::mutex mutex_;
    binding_ptr binding_;

    // 静态依赖节点映射 - 管理全局的属性依赖关系
    static inline std::unordered_map<IProperty*, DependencyNode> dependency_nodes_;
};

/// @brief 创建绑定函数
template<typename F>
[[nodiscard]] auto make_binding(F&& func, const SourceLocation& loc = {}) {
    using ReturnType = std::invoke_result_t<F>;
    return std::make_shared<FunctionBinding<ReturnType, std::decay_t<F>>>(
        std::forward<F>(func), loc
    );
}

/// @brief 绑定工厂函数
template<typename T, BindingFunction<T> F>
[[nodiscard]] auto bind(F&& func, const SourceLocation& loc = {}) {
    return [func = std::forward<F>(func), loc](auto& property) {
        property.set_binding(func, loc);
    };
}

/// @brief 绑定操作符
template<typename T, typename F>
BindableProperty<T>& operator<<=(BindableProperty<T>& property, F&& func) {
    static_assert(BindingFunction<F, T>, "F must be a valid binding function for type T");
    property.set_binding(std::forward<F>(func));
    return property;
}

/// @brief 依赖追踪器 - 使用thread_local实现线程安全的依赖收集
class DependencyTracker {
public:
    /// @brief 获取当前线程的依赖追踪器
    [[nodiscard]] static DependencyTracker& current() {
        thread_local DependencyTracker tracker;
        return tracker;
    }

    /// @brief 依赖收集作用域守卫
    class CollectionScope {
    public:
        explicit CollectionScope(vector<std::weak_ptr<IProperty>>& deps)
            : tracker_(DependencyTracker::current())
            , old_collecting_(tracker_.collecting_)
            , old_dependencies_(std::move(tracker_.current_dependencies_))
            , dependencies_(deps) {
            tracker_.collecting_ = true;
            tracker_.current_dependencies_.clear();
        }

        ~CollectionScope() {
            dependencies_ = std::move(tracker_.current_dependencies_);
            tracker_.current_dependencies_ = std::move(old_dependencies_);
            tracker_.collecting_ = old_collecting_;
        }

        DISABLE_COPY_MOVE(CollectionScope)

    private:
        DependencyTracker& tracker_;
        bool old_collecting_;
        vector<std::weak_ptr<IProperty>> old_dependencies_;
        vector<std::weak_ptr<IProperty>>& dependencies_;
    };

    /// @brief 添加依赖
    void add_dependency(std::weak_ptr<IProperty> property) {
        if (collecting_) {
            // 避免重复添加同一个属性
            auto it = std::find_if(current_dependencies_.begin(), current_dependencies_.end(),
                [&property](const auto& weak_prop) {
                    auto prop1 = weak_prop.lock();
                    auto prop2 = property.lock();
                    return prop1 && prop2 && prop1.get() == prop2.get();
                });

            if (it == current_dependencies_.end()) {
                current_dependencies_.push_back(std::move(property));
            }
        }
    }

    /// @brief 是否正在收集依赖
    [[nodiscard]] bool is_collecting() const noexcept {
        return collecting_;
    }

private:
    bool collecting_ = false;
    vector<std::weak_ptr<IProperty>> current_dependencies_;
};

/// @brief 依赖节点 - 管理单个属性的依赖关系
class DependencyNode {
public:
    /// @brief 添加依赖此属性的观察者
    void add_dependent(std::weak_ptr<IProperty> dependent) {
        std::lock_guard lock(mutex_);
        dependents_.push_back(std::move(dependent));
    }

    /// @brief 移除依赖此属性的观察者
    void remove_dependent(std::weak_ptr<IProperty> dependent) {
        std::lock_guard lock(mutex_);
        auto dependent_ptr = dependent.lock();
        if (!dependent_ptr) return;

        dependents_.erase(
            std::remove_if(dependents_.begin(), dependents_.end(),
                [&dependent_ptr](const auto& weak_dep) {
                    auto dep = weak_dep.lock();
                    return !dep || dep.get() == dependent_ptr.get();
                }),
            dependents_.end()
        );
    }

    /// @brief 通知所有依赖属性
    void notify_dependents() {
        std::lock_guard lock(mutex_);

        // 清理失效的依赖
        dependents_.erase(
            std::remove_if(dependents_.begin(), dependents_.end(),
                [](const auto& weak_dep) { return weak_dep.expired(); }),
            dependents_.end()
        );

        // 通知所有有效的依赖属性
        for (const auto& weak_dependent : dependents_) {
            if (auto dependent = weak_dependent.lock()) {
                // 标记依赖属性为脏状态，触发重新计算
                if (auto bindable = std::dynamic_pointer_cast<IBindableProperty>(dependent)) {
                    bindable->mark_binding_dirty();
                }
            }
        }
    }

    /// @brief 清除所有依赖
    void clear_dependents() {
        std::lock_guard lock(mutex_);
        dependents_.clear();
    }

private:
    std::mutex mutex_;
    vector<std::weak_ptr<IProperty>> dependents_;
};

/// @brief 可绑定属性接口
class IBindableProperty {
public:
    virtual ~IBindableProperty() = default;
    virtual void mark_binding_dirty() = 0;
};

} // namespace property::binding
