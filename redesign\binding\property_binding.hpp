/**
 * @file property_binding.hpp
 * @brief 现代化属性绑定系统
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "../foundation/types.hpp"
#include "../foundation/utility.hpp"
#include "../core/property_base.hpp"
#include "../memory/smart_pointers.hpp"
#include <functional>
#include <concepts>
#include <atomic>
#include <mutex>

namespace property::binding {

using namespace foundation;
using namespace core;
using namespace memory;

/// @brief 绑定状态
enum class BindingState {
    Clean,      ///< 绑定是干净的，值是最新的
    Dirty,      ///< 绑定是脏的，需要重新计算
    Evaluating, ///< 正在计算中
    Error       ///< 计算出错
};

/// @brief 绑定错误信息
struct BindingError {
    string message;
    SourceLocation location;
    
    BindingError(string_view msg = {}, const SourceLocation& loc = {})
        : message(msg), location(loc) {}
};

/// @brief 绑定接口
template<typename T>
class IBinding {
public:
    virtual ~IBinding() = default;
    
    /// @brief 计算绑定值
    [[nodiscard]] virtual T evaluate() = 0;
    
    /// @brief 获取绑定状态
    [[nodiscard]] virtual BindingState state() const noexcept = 0;
    
    /// @brief 标记为脏
    virtual void mark_dirty() noexcept = 0;
    
    /// @brief 是否有错误
    [[nodiscard]] virtual bool has_error() const noexcept = 0;
    
    /// @brief 获取错误信息
    [[nodiscard]] virtual const BindingError& error() const noexcept = 0;
    
    /// @brief 获取依赖的属性
    [[nodiscard]] virtual vector<std::weak_ptr<IProperty>> dependencies() const = 0;
};

/// @brief 函数绑定实现
template<typename T, BindingFunction<T> F>
class FunctionBinding : public IBinding<T> {
public:
    explicit FunctionBinding(F&& func, const SourceLocation& loc = {})
        : function_(std::forward<F>(func))
        , location_(loc)
        , state_(BindingState::Dirty) {}
    
    /// @brief 计算绑定值
    [[nodiscard]] T evaluate() override {
        if (state_ == BindingState::Evaluating) {
            throw std::runtime_error("Circular dependency detected");
        }
        
        if (state_ == BindingState::Clean) {
            return cached_value_;
        }
        
        state_ = BindingState::Evaluating;
        
        try {
            // 开始依赖收集
            DependencyCollector collector;
            auto guard = make_scope_guard([this, &collector]() {
                dependencies_ = std::move(collector.dependencies);
                state_ = BindingState::Clean;
            });
            
            cached_value_ = function_();
            error_ = BindingError{}; // 清除错误
            
            return cached_value_;
        } catch (const std::exception& e) {
            state_ = BindingState::Error;
            error_ = BindingError{e.what(), location_};
            throw;
        }
    }
    
    /// @brief 获取绑定状态
    [[nodiscard]] BindingState state() const noexcept override {
        return state_;
    }
    
    /// @brief 标记为脏
    void mark_dirty() noexcept override {
        state_ = BindingState::Dirty;
    }
    
    /// @brief 是否有错误
    [[nodiscard]] bool has_error() const noexcept override {
        return state_ == BindingState::Error;
    }
    
    /// @brief 获取错误信息
    [[nodiscard]] const BindingError& error() const noexcept override {
        return error_;
    }
    
    /// @brief 获取依赖的属性
    [[nodiscard]] vector<std::weak_ptr<IProperty>> dependencies() const override {
        return dependencies_;
    }

private:
    /// @brief 依赖收集器
    struct DependencyCollector {
        vector<std::weak_ptr<IProperty>> dependencies;
        
        void add_dependency(std::weak_ptr<IProperty> prop) {
            dependencies.push_back(std::move(prop));
        }
    };
    
    F function_;
    SourceLocation location_;
    std::atomic<BindingState> state_;
    mutable T cached_value_{};
    BindingError error_;
    vector<std::weak_ptr<IProperty>> dependencies_;
};

/// @brief 可绑定属性
template<typename T>
class BindableProperty : public PropertyBase<T> {
public:
    using binding_ptr = std::shared_ptr<IBinding<T>>;
    
    /// @brief 构造函数
    explicit BindableProperty(
        string_view name = {},
        const T& initial_value = T{}
    ) : PropertyBase<T>(name, initial_value) {}
    
    /// @brief 是否有绑定
    [[nodiscard]] bool has_binding() const noexcept override {
        std::lock_guard lock(mutex_);
        return binding_ != nullptr;
    }
    
    /// @brief 设置绑定
    template<BindingFunction<T> F>
    void set_binding(F&& func, const SourceLocation& loc = {}) {
        auto new_binding = std::make_shared<FunctionBinding<T, std::decay_t<F>>>(
            std::forward<F>(func), loc
        );
        
        std::lock_guard lock(mutex_);
        binding_ = std::move(new_binding);
        
        // 立即求值
        try {
            this->value_ = binding_->evaluate();
        } catch (...) {
            // 绑定求值失败，保持原值
        }
    }
    
    /// @brief 清除绑定
    void clear_binding() override {
        std::lock_guard lock(mutex_);
        binding_.reset();
    }
    
    /// @brief 获取绑定
    [[nodiscard]] binding_ptr get_binding() const {
        std::lock_guard lock(mutex_);
        return binding_;
    }

protected:
    /// @brief 如果需要则求值绑定
    void evaluate_binding_if_needed() const override {
        std::lock_guard lock(mutex_);
        if (binding_ && binding_->state() == BindingState::Dirty) {
            try {
                this->value_ = binding_->evaluate();
            } catch (...) {
                // 绑定求值失败，保持原值
            }
        }
    }

private:
    mutable std::mutex mutex_;
    binding_ptr binding_;
};

/// @brief 创建绑定函数
template<typename F>
[[nodiscard]] auto make_binding(F&& func, const SourceLocation& loc = {}) {
    using ReturnType = std::invoke_result_t<F>;
    return std::make_shared<FunctionBinding<ReturnType, std::decay_t<F>>>(
        std::forward<F>(func), loc
    );
}

/// @brief 绑定工厂函数
template<typename T, BindingFunction<T> F>
[[nodiscard]] auto bind(F&& func, const SourceLocation& loc = {}) {
    return [func = std::forward<F>(func), loc](auto& property) {
        property.set_binding(func, loc);
    };
}

/// @brief 绑定操作符
template<typename T, BindingFunction<T> F>
BindableProperty<T>& operator<<=(BindableProperty<T>& property, F&& func) {
    property.set_binding(std::forward<F>(func));
    return property;
}

/// @brief 属性依赖追踪
class DependencyTracker {
public:
    /// @brief 获取全局实例
    [[nodiscard]] static DependencyTracker& instance() {
        static DependencyTracker tracker;
        return tracker;
    }
    
    /// @brief 开始依赖收集
    void begin_collection() {
        collecting_ = true;
        current_dependencies_.clear();
    }
    
    /// @brief 结束依赖收集
    [[nodiscard]] vector<std::weak_ptr<IProperty>> end_collection() {
        collecting_ = false;
        return std::move(current_dependencies_);
    }
    
    /// @brief 添加依赖
    void add_dependency(std::weak_ptr<IProperty> property) {
        if (collecting_) {
            current_dependencies_.push_back(std::move(property));
        }
    }
    
    /// @brief 是否正在收集依赖
    [[nodiscard]] bool is_collecting() const noexcept {
        return collecting_;
    }

private:
    std::atomic<bool> collecting_{false};
    vector<std::weak_ptr<IProperty>> current_dependencies_;
};

} // namespace property::binding
