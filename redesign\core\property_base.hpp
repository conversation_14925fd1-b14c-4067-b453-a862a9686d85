/**
 * @file property_base.hpp
 * @brief 属性系统基础类
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "../foundation/types.hpp"
#include "../foundation/utility.hpp"
#include "../foundation/meta_type.hpp"
#include "../memory/smart_pointers.hpp"
#include <functional>
#include <concepts>

namespace property::core {

using namespace foundation;
using namespace memory;

/// @brief 属性变化事件
struct PropertyChangeEvent {
    SourceLocation location;
    string property_name;
    MetaType value_type;
    
    PropertyChangeEvent(
        string_view name = {},
        const MetaType& type = {},
        const SourceLocation& loc = {}
    ) : location(loc), property_name(name), value_type(type) {}
};

/// @brief 属性观察者接口
class IPropertyObserver {
public:
    virtual ~IPropertyObserver() = default;
    
    /// @brief 属性值变化时调用
    virtual void on_property_changed(const PropertyChangeEvent& event) = 0;
    
    /// @brief 属性即将变化时调用
    virtual void on_property_changing(const PropertyChangeEvent& event) {}
};

/// @brief 属性基类接口
class IProperty {
public:
    virtual ~IProperty() = default;
    
    /// @brief 获取属性名称
    [[nodiscard]] virtual string_view name() const noexcept = 0;
    
    /// @brief 获取属性类型信息
    [[nodiscard]] virtual const MetaType& meta_type() const noexcept = 0;
    
    /// @brief 是否有绑定
    [[nodiscard]] virtual bool has_binding() const noexcept = 0;
    
    /// @brief 是否只读
    [[nodiscard]] virtual bool is_readonly() const noexcept = 0;
    
    /// @brief 添加观察者
    virtual void add_observer(std::shared_ptr<IPropertyObserver> observer) = 0;
    
    /// @brief 移除观察者
    virtual void remove_observer(std::shared_ptr<IPropertyObserver> observer) = 0;
    
    /// @brief 清除所有观察者
    virtual void clear_observers() = 0;
    
    /// @brief 获取类型擦除的值
    [[nodiscard]] virtual TypeErasedValue get_value() const = 0;
    
    /// @brief 设置类型擦除的值
    virtual void set_value(const TypeErasedValue& value) = 0;
};

/// @brief 属性基类实现
template<typename T>
class PropertyBase : public IProperty {
    static_assert(PropertyValue<T>, "T must be a valid property value type");
public:
    using value_type = T;
    using reference = T&;
    using const_reference = const T&;
    using observer_ptr = std::shared_ptr<IPropertyObserver>;
    
    /// @brief 构造函数
    explicit PropertyBase(
        string_view name = {},
        const T& initial_value = T{},
        bool readonly = false
    ) : name_(name)
      , value_(initial_value)
      , readonly_(readonly)
      , meta_type_(MetaType::from_type<T>()) {}
    
    /// @brief 析构函数
    ~PropertyBase() override = default;
    
    /// @brief 获取属性名称
    [[nodiscard]] string_view name() const noexcept override {
        return name_;
    }
    
    /// @brief 获取属性类型信息
    [[nodiscard]] const MetaType& meta_type() const noexcept override {
        return meta_type_;
    }
    
    /// @brief 是否只读
    [[nodiscard]] bool is_readonly() const noexcept override {
        return readonly_;
    }
    
    /// @brief 获取值
    [[nodiscard]] const_reference get() const noexcept {
        evaluate_binding_if_needed();
        return value_;
    }
    
    /// @brief 设置值
    void set(const T& new_value) {
        if (readonly_) {
            return;
        }
        
        if constexpr (EqualityComparable<T>) {
            if (value_ == new_value) {
                return;
            }
        }
        
        // 通知即将变化
        PropertyChangeEvent event{name_, meta_type_};
        notify_changing(event);
        
        // 清除绑定
        clear_binding();
        
        // 设置新值
        value_ = new_value;
        
        // 通知已变化
        notify_changed(event);
    }
    
    /// @brief 设置值（移动语义）
    void set(T&& new_value) {
        if (readonly_) {
            return;
        }
        
        if constexpr (EqualityComparable<T>) {
            if (value_ == new_value) {
                return;
            }
        }
        
        PropertyChangeEvent event{name_, meta_type_};
        notify_changing(event);
        
        clear_binding();
        value_ = std::move(new_value);
        
        notify_changed(event);
    }
    
    /// @brief 添加观察者
    void add_observer(observer_ptr observer) override {
        if (observer) {
            observers_.push_back(std::move(observer));
        }
    }
    
    /// @brief 移除观察者
    void remove_observer(observer_ptr observer) override {
        observers_.erase(
            std::remove_if(observers_.begin(), observers_.end(),
                [&observer](const auto& weak_obs) {
                    return weak_obs.expired() || weak_obs.lock() == observer;
                }),
            observers_.end()
        );
    }
    
    /// @brief 清除所有观察者
    void clear_observers() override {
        observers_.clear();
    }
    
    /// @brief 获取类型擦除的值
    [[nodiscard]] TypeErasedValue get_value() const override {
        return TypeErasedValue{get()};
    }
    
    /// @brief 设置类型擦除的值
    void set_value(const TypeErasedValue& value) override {
        if (value.meta_type() == meta_type_) {
            set(value.get<T>());
        }
    }
    
    /// @brief 运算符重载
    [[nodiscard]] const_reference operator*() const noexcept {
        return get();
    }
    
    PropertyBase& operator=(const T& value) {
        set(value);
        return *this;
    }
    
    PropertyBase& operator=(T&& value) {
        set(std::move(value));
        return *this;
    }
    
    /// @brief 隐式转换到值类型
    [[nodiscard]] operator const_reference() const noexcept {
        return get();
    }

protected:
    /// @brief 通知即将变化
    virtual void notify_changing(const PropertyChangeEvent& event) {
        for (auto& observer : observers_) {
            if (auto obs = observer.lock()) {
                obs->on_property_changing(event);
            }
        }
        // 清理失效的观察者
        std::erase_if(observers_, [](const auto& weak_obs) {
            return weak_obs.expired();
        });
    }
    
    /// @brief 通知已变化
    virtual void notify_changed(const PropertyChangeEvent& event) {
        for (auto& observer : observers_) {
            if (auto obs = observer.lock()) {
                obs->on_property_changed(event);
            }
        }
        // 清理失效的观察者
        std::erase_if(observers_, [](const auto& weak_obs) {
            return weak_obs.expired();
        });
    }
    
    /// @brief 如果需要则求值绑定
    virtual void evaluate_binding_if_needed() const {}
    
    /// @brief 清除绑定
    virtual void clear_binding() {}
    
    /// @brief 设置只读状态
    void set_readonly(bool readonly) noexcept {
        readonly_ = readonly;
    }

private:
    string name_;
    mutable T value_;
    bool readonly_;
    MetaType meta_type_;
    vector<std::weak_ptr<IPropertyObserver>> observers_;
};

/// @brief 简单属性（无绑定功能）
template<typename T>
class SimpleProperty : public PropertyBase<T> {
    static_assert(PropertyValue<T>, "T must be a valid property value type");
public:
    using PropertyBase<T>::PropertyBase;
    
    /// @brief 是否有绑定
    [[nodiscard]] bool has_binding() const noexcept override {
        return false;
    }
};

/// @brief 只读属性
template<typename T>
class ReadOnlyProperty : public PropertyBase<T> {
    static_assert(PropertyValue<T>, "T must be a valid property value type");
public:
    explicit ReadOnlyProperty(
        string_view name = {},
        const T& initial_value = T{}
    ) : PropertyBase<T>(name, initial_value, true) {}
    
    /// @brief 是否有绑定
    [[nodiscard]] bool has_binding() const noexcept override {
        return false;
    }
    
    /// @brief 设置值（仅供内部使用）
    void set_internal(const T& value) {
        this->set_readonly(false);
        this->set(value);
        this->set_readonly(true);
    }
    
    void set_internal(T&& value) {
        this->set_readonly(false);
        this->set(std::move(value));
        this->set_readonly(true);
    }
};

} // namespace property::core
