#ifndef ATTRIBUTES_H
#define ATTRIBUTES_H

#include <vector>
#include <memory>
#include <stdexcept>
#include "attribute.h"

class Attributes
{
public:
    template<typename T>
    void addAttribute(const T& value)
    {
        attribs_.emplace_back(std::make_shared<Attribute<T>>(value));
    }

    size_t size() const
    {
        return attribs_.size();
    }

    Attrib& operator[](size_t index)
    {
        return *attribs_.at(index);
    }

    const Attrib& operator[](size_t index) const
    {
        return getAttribute(index);
    }

    const Attrib& getAttribute(size_t index) const
    {
        if (index < attribs_.size()) {
            return *attribs_[index];
        } else {
            throw std::out_of_range("Index out of range");
        }
    }

    class iterator
    {
    private:
        typename std::vector<std::shared_ptr<Attrib>>::iterator current;

    public:
        iterator(typename std::vector<std::shared_ptr<Attrib>>::iterator itr)
            : current(itr) {}

        // 前缀递增
        iterator& operator++()
        {
            ++current;
            return *this;
        }

        // 后缀递增
        iterator operator++(int)
        {
            iterator temp = *this;
            ++current;
            return temp;
        }

        // 解引用
        std::shared_ptr<Attrib>& operator*()
        {
            return *current;
        }

        // 比较运算符
        bool operator==(const iterator& other) const
        {
            return current == other.current;
        }

        bool operator!=(const iterator& other) const
        {
            return current != other.current;
        }
    };

    // 返回begin迭代器
    iterator begin()
    {
        return iterator(attribs_.begin());
    }

    // 返回end迭代器
    iterator end()
    {
        return iterator(attribs_.end());
    }

private:
    std::vector<std::shared_ptr<Attrib>> attribs_;
};

#endif // ATTRIBUTES_H
