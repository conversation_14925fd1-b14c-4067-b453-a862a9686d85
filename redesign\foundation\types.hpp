/**
 * @file types.hpp
 * @brief 现代化C++基础类型定义
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include <cstdint>
#include <cstddef>
#include <limits>
#include <string>
#include <string_view>
#include <concepts>
#include <type_traits>

namespace property::foundation {

/// @brief 基础整数类型定义
using i8  = std::int8_t;
using i16 = std::int16_t;
using i32 = std::int32_t;
using i64 = std::int64_t;

using u8  = std::uint8_t;
using u16 = std::uint16_t;
using u32 = std::uint32_t;
using u64 = std::uint64_t;

/// @brief 浮点类型定义
using f32 = float;
using f64 = double;

/// @brief 大小和索引类型
using size_type = std::size_t;
using index_type = std::ptrdiff_t;

/// @brief 字符串类型
using string = std::string;
using string_view = std::string_view;

/// @brief 常用常量
inline constexpr size_type invalid_index = std::numeric_limits<size_type>::max();
inline constexpr size_type default_capacity = 16;

/// @brief 类型特征和概念
template<typename T>
concept Arithmetic = std::is_arithmetic_v<T>;

template<typename T>
concept Integral = std::is_integral_v<T>;

template<typename T>
concept FloatingPoint = std::is_floating_point_v<T>;

template<typename T>
concept Enum = std::is_enum_v<T>;

template<typename T>
concept Pointer = std::is_pointer_v<T>;

template<typename T>
concept Trivial = std::is_trivial_v<T>;

template<typename T>
concept TriviallyDestructible = std::is_trivially_destructible_v<T>;

template<typename T>
concept NoThrowMovable = std::is_nothrow_move_constructible_v<T> && 
                        std::is_nothrow_move_assignable_v<T>;

template<typename T>
concept NoThrowCopyable = std::is_nothrow_copy_constructible_v<T> && 
                         std::is_nothrow_copy_assignable_v<T>;

template<typename T>
concept Hashable = requires(const T& t) {
    { std::hash<T>{}(t) } -> std::convertible_to<std::size_t>;
};

template<typename T>
concept EqualityComparable = requires(const T& a, const T& b) {
    { a == b } -> std::convertible_to<bool>;
    { a != b } -> std::convertible_to<bool>;
};

template<typename T>
concept LessThanComparable = requires(const T& a, const T& b) {
    { a < b } -> std::convertible_to<bool>;
};

template<typename T>
concept Comparable = EqualityComparable<T> && LessThanComparable<T>;

/// @brief 函数式编程支持
template<typename F, typename... Args>
concept Invocable = std::invocable<F, Args...>;

template<typename F, typename R, typename... Args>
concept InvocableR = std::is_invocable_r_v<R, F, Args...>;

/// @brief 容器相关概念
template<typename T>
concept Container = requires(T t) {
    typename T::value_type;
    typename T::size_type;
    typename T::iterator;
    typename T::const_iterator;
    { t.begin() } -> std::same_as<typename T::iterator>;
    { t.end() } -> std::same_as<typename T::iterator>;
    { t.size() } -> std::convertible_to<typename T::size_type>;
    { t.empty() } -> std::convertible_to<bool>;
};

template<typename T>
concept RandomAccessContainer = Container<T> && requires(T t, typename T::size_type n) {
    { t[n] } -> std::convertible_to<typename T::value_type&>;
};

/// @brief 智能指针相关概念
template<typename T>
concept SmartPointer = requires(T t) {
    { t.get() } -> std::convertible_to<typename T::element_type*>;
    { t.reset() } -> std::same_as<void>;
    { static_cast<bool>(t) } -> std::same_as<bool>;
};

/// @brief 属性系统相关概念
template<typename T>
concept PropertyValue = std::copyable<T> && std::destructible<T>;

template<typename T>
concept Observable = requires(T t) {
    { t.value() } -> PropertyValue;
    t.setValue(typename T::value_type{});
};

/// @brief 绑定相关概念
template<typename F>
concept BindingFunction = std::invocable<F> && 
                         PropertyValue<std::invoke_result_t<F>>;

/// @brief 观察者相关概念
template<typename F>
concept Observer = std::invocable<F> && 
                  std::is_void_v<std::invoke_result_t<F>>;

} // namespace property::foundation
