/**
 * @file property_observer.hpp
 * @brief 现代化属性观察者系统
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "../foundation/types.hpp"
#include "../foundation/utility.hpp"
#include "../core/property_base.hpp"
#include "../memory/smart_pointers.hpp"
#include <functional>
#include <concepts>
#include <ranges>
#include <algorithm>

namespace property::observer {

using namespace foundation;
using namespace core;
using namespace memory;

/// @brief 观察者优先级
enum class ObserverPriority : i32 {
    Lowest = 0,
    Low = 25,
    Normal = 50,
    High = 75,
    Highest = 100
};

/// @brief 函数式观察者
template<typename T>
class FunctionObserver : public IPropertyObserver {
public:
    using callback_type = std::function<void(const T&, const T&)>;
    
    /// @brief 构造函数
    explicit FunctionObserver(
        callback_type callback,
        ObserverPriority priority = ObserverPriority::Normal
    ) : callback_(std::move(callback)), priority_(priority) {}
    
    /// @brief 属性值变化时调用
    void on_property_changed(const PropertyChangeEvent& event) override {
        if (callback_ && event.value_type == MetaType::from_type<T>()) {
            // 这里需要从事件中获取旧值和新值
            // 简化实现，实际应该从事件中提取值
            T old_value{};
            T new_value{};
            callback_(old_value, new_value);
        }
    }
    
    /// @brief 获取优先级
    [[nodiscard]] ObserverPriority priority() const noexcept {
        return priority_;
    }

private:
    callback_type callback_;
    ObserverPriority priority_;
};

/// @brief 简单观察者（无参数回调）
class SimpleObserver : public IPropertyObserver {
public:
    using callback_type = std::function<void()>;
    
    /// @brief 构造函数
    explicit SimpleObserver(
        callback_type callback,
        ObserverPriority priority = ObserverPriority::Normal
    ) : callback_(std::move(callback)), priority_(priority) {}
    
    /// @brief 属性值变化时调用
    void on_property_changed(const PropertyChangeEvent& event) override {
        if (callback_) {
            callback_();
        }
    }
    
    /// @brief 获取优先级
    [[nodiscard]] ObserverPriority priority() const noexcept {
        return priority_;
    }

private:
    callback_type callback_;
    ObserverPriority priority_;
};

/// @brief 条件观察者
template<typename T>
class ConditionalObserver : public IPropertyObserver {
public:
    using callback_type = std::function<void(const T&, const T&)>;
    using condition_type = std::function<bool(const T&, const T&)>;
    
    /// @brief 构造函数
    ConditionalObserver(
        callback_type callback,
        condition_type condition,
        ObserverPriority priority = ObserverPriority::Normal
    ) : callback_(std::move(callback))
      , condition_(std::move(condition))
      , priority_(priority) {}
    
    /// @brief 属性值变化时调用
    void on_property_changed(const PropertyChangeEvent& event) override {
        if (callback_ && condition_ && event.value_type == MetaType::from_type<T>()) {
            T old_value{};
            T new_value{};
            if (condition_(old_value, new_value)) {
                callback_(old_value, new_value);
            }
        }
    }
    
    /// @brief 获取优先级
    [[nodiscard]] ObserverPriority priority() const noexcept {
        return priority_;
    }

private:
    callback_type callback_;
    condition_type condition_;
    ObserverPriority priority_;
};

/// @brief 观察者管理器
class ObserverManager {
public:
    using observer_ptr = std::shared_ptr<IPropertyObserver>;
    using weak_observer_ptr = std::weak_ptr<IPropertyObserver>;
    
    /// @brief 添加观察者
    void add_observer(observer_ptr observer, ObserverPriority priority = ObserverPriority::Normal) {
        if (!observer) {
            return;
        }
        
        std::lock_guard lock(mutex_);
        observers_.emplace_back(std::move(observer), priority);
        
        // 按优先级排序
        std::ranges::sort(observers_, [](const auto& a, const auto& b) {
            return static_cast<i32>(a.second) > static_cast<i32>(b.second);
        });
    }
    
    /// @brief 移除观察者
    void remove_observer(const observer_ptr& observer) {
        std::lock_guard lock(mutex_);
        observers_.erase(
            std::remove_if(observers_.begin(), observers_.end(),
                [&observer](const auto& pair) {
                    return pair.first.expired() || pair.first.lock() == observer;
                }),
            observers_.end()
        );
    }
    
    /// @brief 清除所有观察者
    void clear_observers() {
        std::lock_guard lock(mutex_);
        observers_.clear();
    }
    
    /// @brief 通知所有观察者
    void notify_observers(const PropertyChangeEvent& event) {
        std::lock_guard lock(mutex_);
        
        // 清理失效的观察者
        observers_.erase(
            std::remove_if(observers_.begin(), observers_.end(),
                [](const auto& pair) { return pair.first.expired(); }),
            observers_.end()
        );
        
        // 通知所有有效的观察者
        for (const auto& [weak_observer, priority] : observers_) {
            if (auto observer = weak_observer.lock()) {
                try {
                    observer->on_property_changed(event);
                } catch (...) {
                    // 忽略观察者中的异常，避免影响其他观察者
                }
            }
        }
    }
    
    /// @brief 获取观察者数量
    [[nodiscard]] size_type observer_count() const {
        std::lock_guard lock(mutex_);
        return observers_.size();
    }
    
    /// @brief 是否为空
    [[nodiscard]] bool empty() const {
        std::lock_guard lock(mutex_);
        return observers_.empty();
    }

private:
    mutable std::mutex mutex_;
    vector<std::pair<weak_observer_ptr, ObserverPriority>> observers_;
};

/// @brief 观察者工厂函数
template<typename T>
[[nodiscard]] auto make_observer(
    std::function<void(const T&, const T&)> callback,
    ObserverPriority priority = ObserverPriority::Normal
) {
    return std::make_shared<FunctionObserver<T>>(std::move(callback), priority);
}

[[nodiscard]] inline auto make_simple_observer(
    std::function<void()> callback,
    ObserverPriority priority = ObserverPriority::Normal
) {
    return std::make_shared<SimpleObserver>(std::move(callback), priority);
}

template<typename T>
[[nodiscard]] auto make_conditional_observer(
    std::function<void(const T&, const T&)> callback,
    std::function<bool(const T&, const T&)> condition,
    ObserverPriority priority = ObserverPriority::Normal
) {
    return std::make_shared<ConditionalObserver<T>>(
        std::move(callback), std::move(condition), priority
    );
}

/// @brief 观察者RAII包装器
template<typename PropertyType>
class ScopedObserver {
public:
    /// @brief 构造函数
    template<typename F>
    ScopedObserver(PropertyType& property, F&& callback, ObserverPriority priority = ObserverPriority::Normal)
        : property_(property) {
        if constexpr (std::is_invocable_v<F>) {
            observer_ = make_simple_observer(std::forward<F>(callback), priority);
        } else {
            observer_ = make_observer<typename PropertyType::value_type>(std::forward<F>(callback), priority);
        }
        property_.add_observer(observer_);
    }
    
    /// @brief 析构函数
    ~ScopedObserver() {
        if (observer_) {
            property_.remove_observer(observer_);
        }
    }
    
    /// @brief 获取观察者
    [[nodiscard]] auto get_observer() const noexcept {
        return observer_;
    }
    
    DISABLE_COPY_MOVE(ScopedObserver)

private:
    PropertyType& property_;
    std::shared_ptr<IPropertyObserver> observer_;
};

/// @brief 创建作用域观察者
template<typename PropertyType, typename F>
[[nodiscard]] auto make_scoped_observer(
    PropertyType& property, 
    F&& callback, 
    ObserverPriority priority = ObserverPriority::Normal
) {
    return ScopedObserver<PropertyType>(property, std::forward<F>(callback), priority);
}

/// @brief 观察者链
template<typename T>
class ObserverChain {
public:
    using callback_type = std::function<void(const T&, const T&)>;
    
    /// @brief 添加观察者到链中
    ObserverChain& then(callback_type callback) {
        callbacks_.push_back(std::move(callback));
        return *this;
    }
    
    /// @brief 执行观察者链
    void execute(const T& old_value, const T& new_value) const {
        for (const auto& callback : callbacks_) {
            if (callback) {
                try {
                    callback(old_value, new_value);
                } catch (...) {
                    // 继续执行链中的其他观察者
                }
            }
        }
    }
    
    /// @brief 转换为观察者
    [[nodiscard]] auto to_observer(ObserverPriority priority = ObserverPriority::Normal) const {
        return make_observer<T>(
            [chain = *this](const T& old_val, const T& new_val) {
                chain.execute(old_val, new_val);
            },
            priority
        );
    }

private:
    vector<callback_type> callbacks_;
};

/// @brief 创建观察者链
template<typename T>
[[nodiscard]] auto make_observer_chain() {
    return ObserverChain<T>{};
}

} // namespace property::observer
