#ifndef BASE_PROPERTY_H
#define BASE_PROPERTY_H

#include <functional>
#include <memory>
#include <vector>
#include <cassert>

template <typename T>
class Property;

class UntypedPropertyBinding {
public:
    using BindingFunction = std::function<void()>;

    UntypedPropertyBinding() = default;
    UntypedPropertyBinding(BindingFunction func) : bindingFunction(std::move(func)) {}

    void evaluate() const {
        if (bindingFunction) {
            bindingFunction();
        }
    }

private:
    BindingFunction bindingFunction;
};

template <typename T>
class Property {
public:
    using ValueType = T;
    using BindingFunction = std::function<T()>;

    Property() = default;
    Property(const T& value) : value_(value) {}

    T value() const {
        if (binding_) {
            binding_->evaluate();
        }
        return value_;
    }

    void setValue(const T& value) {
        value_ = value;
        notifyDependents();
    }

    void bind(BindingFunction func) {
        binding_ = std::make_shared<UntypedPropertyBinding>([this, func]() {
            this->value_ = func();
        });
        notifyDependents();
    }

    void addDependent(Property* dependent) {
        dependents_.push_back(dependent);
    }

private:
    void notifyDependents() const {
        for (auto* dependent : dependents_) {
            dependent->binding_->evaluate();
        }
    }

    T value_{};
    std::shared_ptr<UntypedPropertyBinding> binding_;
    std::vector<Property*> dependents_;
};

#endif // BASE_PROPERTY_H
