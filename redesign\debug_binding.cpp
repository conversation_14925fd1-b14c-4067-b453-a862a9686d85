/**
 * @file debug_binding.cpp
 * @brief 调试绑定系统
 */

#include "property.hpp"
#include <iostream>

using namespace property;

int main() {
    std::cout << "=== Binding System Debug ===" << std::endl;
    
    auto x = make_property<double>("x", 3.0);
    auto y = make_property<double>("y", 4.0);
    auto sum = make_property<double>("sum");
    
    std::cout << "Initial values:" << std::endl;
    std::cout << "x = " << x.get() << std::endl;
    std::cout << "y = " << y.get() << std::endl;
    std::cout << "sum = " << sum.get() << std::endl;
    
    std::cout << "\nSetting binding: sum = x + y" << std::endl;
    bind_to(sum, [&]() {
        std::cout << "  Evaluating binding: x=" << x.get() << ", y=" << y.get() << std::endl;
        return x.get() + y.get();
    });

    std::cout << "Dependencies established for sum" << std::endl;
    
    std::cout << "After binding:" << std::endl;
    std::cout << "sum = " << sum.get() << std::endl;
    std::cout << "sum has binding: " << (sum.has_binding() ? "Yes" : "No") << std::endl;
    
    std::cout << "\nChanging x to 5.0" << std::endl;
    x = 5.0;
    std::cout << "x = " << x.get() << std::endl;
    std::cout << "sum = " << sum.get() << " (should be 9.0)" << std::endl;
    
    std::cout << "\nChanging y to 6.0" << std::endl;
    y = 6.0;
    std::cout << "y = " << y.get() << std::endl;
    std::cout << "sum = " << sum.get() << " (should be 11.0)" << std::endl;
    
    return 0;
}
