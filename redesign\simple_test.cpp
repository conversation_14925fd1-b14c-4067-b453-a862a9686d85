/**
 * @file simple_test.cpp
 * @brief 简化的编译和功能测试
 */

#include "property.hpp"
#include <iostream>

using namespace property;

int main() {
    std::cout << "=== Modern Property System Test ===" << std::endl;
    std::cout << "Version: " << PROPERTY_VERSION << std::endl;
    
    try {
        // 基本属性测试
        std::cout << "\n1. Basic Property Test:" << std::endl;
        auto name = make_property<std::string>("name", "<PERSON>");
        auto age = make_property<int>("age", 25);
        
        std::cout << "Name: " << name.get() << std::endl;
        std::cout << "Age: " << age.get() << std::endl;
        
        // 修改值
        name = "Jane";
        age = 30;
        
        std::cout << "Updated Name: " << name.get() << std::endl;
        std::cout << "Updated Age: " << age.get() << std::endl;
        
        // 只读属性测试
        std::cout << "\n2. ReadOnly Property Test:" << std::endl;
        auto is_adult = make_readonly_property<bool>("is_adult", false);
        std::cout << "Is Adult: " << (is_adult.get() ? "Yes" : "No") << std::endl;
        
        // 绑定测试
        std::cout << "\n3. Binding Test:" << std::endl;
        auto x = make_property<double>("x", 3.0);
        auto y = make_property<double>("y", 4.0);
        auto distance = make_property<double>("distance");
        
        bind_to(distance, [&]() {
            return std::sqrt(x.get() * x.get() + y.get() * y.get());
        });
        
        std::cout << "Distance: " << distance.get() << std::endl;

        x = 6.0;
        y = 8.0;
        std::cout << "Updated Distance: " << distance.get() << std::endl;

        // 手动测试绑定更新
        std::cout << "Manual binding test:" << std::endl;
        auto a = make_property<int>("a", 10);
        auto b = make_property<int>("b");

        bind_to(b, [&]() {
            std::cout << "  Computing b = a * 2, a = " << a.get() << std::endl;
            return a.get() * 2;
        });

        std::cout << "Initial b: " << b.get() << std::endl;
        a = 20;
        std::cout << "After a=20, b: " << b.get() << std::endl;
        
        // 观察者测试
        std::cout << "\n4. Observer Test:" << std::endl;
        auto counter = make_property<int>("counter", 0);
        int notification_count = 0;
        
        auto observer = observe(counter, [&]() {
            ++notification_count;
            std::cout << "Counter changed to: " << counter.get() << std::endl;
        });
        
        counter = 1;
        counter = 2;
        counter = 3;
        
        std::cout << "Total notifications: " << notification_count << std::endl;
        
        std::cout << "\n=== All Tests Passed! ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
