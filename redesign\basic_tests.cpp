/**
 * @file basic_tests.cpp
 * @brief 基础功能测试
 */

#include "property.hpp"
#include <iostream>
#include <cassert>
#include <chrono>
#include <vector>
#include <memory>

using namespace property;

void test_basic_properties() {
    std::cout << "Testing basic properties..." << std::endl;
    
    // 创建属性
    auto name = make_property<std::string>("name", "<PERSON>");
    auto age = make_property<int>("age", 25);
    
    // 测试基本功能
    assert(name.get() == "<PERSON>");
    assert(age.get() == 25);
    
    // 测试设置值
    name = "<PERSON>";
    age = 30;
    
    assert(name.get() == "Jane");
    assert(age.get() == 30);
    
    std::cout << "✓ Basic properties test passed" << std::endl;
}

void test_readonly_properties() {
    std::cout << "Testing readonly properties..." << std::endl;
    
    auto readonly_prop = make_readonly_property<int>("readonly", 42);
    
    assert(readonly_prop.get() == 42);
    assert(readonly_prop.is_readonly());
    
    // 尝试设置值应该被忽略
    readonly_prop.set(100);
    assert(readonly_prop.get() == 42);
    
    std::cout << "✓ Readonly properties test passed" << std::endl;
}

void test_binding_system() {
    std::cout << "Testing binding system..." << std::endl;
    
    auto x = make_property<double>("x", 3.0);
    auto y = make_property<double>("y", 4.0);
    auto sum = make_property<double>("sum");
    
    // 设置绑定
    bind_to(sum, [&]() {
        return x.get() + y.get();
    });
    
    // 测试初始值
    assert(sum.get() == 7.0);
    
    // 修改源值并测试绑定更新
    x = 5.0;
    assert(sum.get() == 9.0); // 5 + 4
    
    y = 6.0;
    assert(sum.get() == 11.0); // 5 + 6
    
    std::cout << "✓ Binding system test passed" << std::endl;
}

void test_observer_system() {
    std::cout << "Testing observer system..." << std::endl;
    
    auto counter = make_property<int>("counter", 0);
    int notification_count = 0;
    
    // 添加观察者
    auto observer = observe(counter, [&]() {
        ++notification_count;
    });
    
    // 修改值并检查通知
    counter = 1;
    assert(notification_count == 1);
    
    counter = 2;
    assert(notification_count == 2);
    
    counter = 3;
    assert(notification_count == 3);
    
    std::cout << "✓ Observer system test passed" << std::endl;
}

void test_complex_binding() {
    std::cout << "Testing complex binding..." << std::endl;
    
    auto a = make_property<int>("a", 10);
    auto b = make_property<int>("b");
    auto c = make_property<int>("c");
    
    // 设置绑定链
    bind_to(b, [&]() { return a.get() * 2; });
    bind_to(c, [&]() { return b.get() + 5; });
    
    // 测试初始值
    assert(b.get() == 20); // 10 * 2
    assert(c.get() == 25); // 20 + 5
    
    // 修改源值
    a = 15;
    assert(b.get() == 30); // 15 * 2
    assert(c.get() == 35); // 30 + 5
    
    std::cout << "✓ Complex binding test passed" << std::endl;
}

void test_type_safety() {
    std::cout << "Testing type safety..." << std::endl;
    
    auto int_prop = make_property<int>("int", 42);
    auto string_prop = make_property<std::string>("string", "hello");
    auto bool_prop = make_property<bool>("bool", true);
    
    // 测试类型信息
    assert(int_prop.meta_type().size() == sizeof(int));
    assert(string_prop.meta_type().size() == sizeof(std::string));
    assert(bool_prop.meta_type().size() == sizeof(bool));
    
    std::cout << "✓ Type safety test passed" << std::endl;
}

void test_performance() {
    std::cout << "Testing performance..." << std::endl;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // 创建大量属性
    constexpr int count = 1000;
    std::vector<std::unique_ptr<Property<int>>> properties;
    
    for (int i = 0; i < count; ++i) {
        properties.push_back(std::make_unique<Property<int>>("prop" + std::to_string(i), i));
    }
    
    // 修改所有属性
    for (int i = 0; i < count; ++i) {
        *properties[i] = i * 2;
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "Created and modified " << count << " properties in " 
              << duration.count() << " microseconds" << std::endl;
    
    std::cout << "✓ Performance test completed" << std::endl;
}

int main() {
    std::cout << "=== Property System Basic Tests ===" << std::endl;
    std::cout << "Version: " << PROPERTY_VERSION << std::endl;
    std::cout << std::endl;
    
    try {
        test_basic_properties();
        test_readonly_properties();
        test_binding_system();
        test_observer_system();
        test_complex_binding();
        test_type_safety();
        test_performance();
        
        std::cout << std::endl;
        std::cout << "🎉 All tests passed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Test failed: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
