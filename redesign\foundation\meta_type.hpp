/**
 * @file meta_type.hpp
 * @brief 现代化类型信息系统
 * <AUTHOR> Agent
 * @date 2025-07-10
 */

#pragma once

#include "types.hpp"
#include "utility.hpp"
#include <typeinfo>
#include <typeindex>
#include <any>
#include <functional>

namespace property::foundation {

/// @brief 类型ID
using type_id = std::type_index;

/// @brief 获取类型ID
template<typename T>
[[nodiscard]] constexpr type_id get_type_id() noexcept {
    return std::type_index(typeid(T));
}

/// @brief 类型信息
class MetaType {
public:
    /// @brief 默认构造函数
    MetaType() noexcept = default;
    
    /// @brief 从类型构造
    template<typename T>
    [[nodiscard]] static MetaType from_type() noexcept {
        MetaType meta;
        meta.id_ = get_type_id<T>();
        meta.name_ = typeid(T).name();
        meta.size_ = sizeof(T);
        meta.alignment_ = alignof(T);
        meta.is_trivial_ = std::is_trivial_v<T>;
        meta.is_trivially_destructible_ = std::is_trivially_destructible_v<T>;
        meta.is_nothrow_move_constructible_ = std::is_nothrow_move_constructible_v<T>;
        meta.is_nothrow_copy_constructible_ = std::is_nothrow_copy_constructible_v<T>;

        meta.construct_ = [](void* ptr) { new(ptr) T{}; };
        meta.destruct_ = [](void* ptr) { static_cast<T*>(ptr)->~T(); };
        meta.copy_construct_ = [](void* dst, const void* src) {
            new(dst) T(*static_cast<const T*>(src));
        };
        meta.move_construct_ = [](void* dst, void* src) {
            new(dst) T(std::move(*static_cast<T*>(src)));
        };
        meta.copy_assign_ = [](void* dst, const void* src) {
            *static_cast<T*>(dst) = *static_cast<const T*>(src);
        };
        meta.move_assign_ = [](void* dst, void* src) {
            *static_cast<T*>(dst) = std::move(*static_cast<T*>(src));
        };
        meta.equal_ = [](const void* lhs, const void* rhs) -> bool {
            if constexpr (EqualityComparable<T>) {
                return *static_cast<const T*>(lhs) == *static_cast<const T*>(rhs);
            } else {
                return false;
            }
        };
        meta.hash_ = [](const void* ptr) -> size_type {
            if constexpr (Hashable<T>) {
                return std::hash<T>{}(*static_cast<const T*>(ptr));
            } else {
                return 0;
            }
        };

        return meta;
    }
    
    /// @brief 获取类型ID
    [[nodiscard]] type_id id() const noexcept { return id_; }
    
    /// @brief 获取类型名称
    [[nodiscard]] constexpr const char* name() const noexcept { return name_; }
    
    /// @brief 获取类型大小
    [[nodiscard]] constexpr size_type size() const noexcept { return size_; }
    
    /// @brief 获取类型对齐
    [[nodiscard]] constexpr size_type alignment() const noexcept { return alignment_; }
    
    /// @brief 是否为平凡类型
    [[nodiscard]] constexpr bool is_trivial() const noexcept { return is_trivial_; }
    
    /// @brief 是否为平凡析构类型
    [[nodiscard]] constexpr bool is_trivially_destructible() const noexcept { 
        return is_trivially_destructible_; 
    }
    
    /// @brief 是否为无抛出移动构造类型
    [[nodiscard]] constexpr bool is_nothrow_move_constructible() const noexcept { 
        return is_nothrow_move_constructible_; 
    }
    
    /// @brief 是否为无抛出拷贝构造类型
    [[nodiscard]] constexpr bool is_nothrow_copy_constructible() const noexcept { 
        return is_nothrow_copy_constructible_; 
    }
    
    /// @brief 构造对象
    void construct(void* ptr) const {
        if (construct_) {
            construct_(ptr);
        }
    }
    
    /// @brief 析构对象
    void destruct(void* ptr) const {
        if (destruct_) {
            destruct_(ptr);
        }
    }
    
    /// @brief 拷贝构造对象
    void copy_construct(void* dst, const void* src) const {
        if (copy_construct_) {
            copy_construct_(dst, src);
        }
    }
    
    /// @brief 移动构造对象
    void move_construct(void* dst, void* src) const {
        if (move_construct_) {
            move_construct_(dst, src);
        }
    }
    
    /// @brief 拷贝赋值对象
    void copy_assign(void* dst, const void* src) const {
        if (copy_assign_) {
            copy_assign_(dst, src);
        }
    }
    
    /// @brief 移动赋值对象
    void move_assign(void* dst, void* src) const {
        if (move_assign_) {
            move_assign_(dst, src);
        }
    }
    
    /// @brief 比较对象是否相等
    [[nodiscard]] bool equal(const void* lhs, const void* rhs) const {
        return equal_ ? equal_(lhs, rhs) : false;
    }
    
    /// @brief 计算对象哈希值
    [[nodiscard]] size_type hash(const void* ptr) const {
        return hash_ ? hash_(ptr) : 0;
    }
    
    /// @brief 比较运算符
    [[nodiscard]] bool operator==(const MetaType& other) const noexcept {
        return id_ == other.id_;
    }

    [[nodiscard]] bool operator!=(const MetaType& other) const noexcept {
        return !(*this == other);
    }

private:
    type_id id_{typeid(void)};
    const char* name_ = "void";
    size_type size_ = 0;
    size_type alignment_ = 1;
    bool is_trivial_ = false;
    bool is_trivially_destructible_ = false;
    bool is_nothrow_move_constructible_ = false;
    bool is_nothrow_copy_constructible_ = false;
    
    std::function<void(void*)> construct_;
    std::function<void(void*)> destruct_;
    std::function<void(void*, const void*)> copy_construct_;
    std::function<void(void*, void*)> move_construct_;
    std::function<void(void*, const void*)> copy_assign_;
    std::function<void(void*, void*)> move_assign_;
    std::function<bool(const void*, const void*)> equal_;
    std::function<size_type(const void*)> hash_;
};

/// @brief 类型擦除值容器
class TypeErasedValue {
public:
    /// @brief 默认构造函数
    TypeErasedValue() = default;
    
    /// @brief 从值构造
    template<typename T>
    explicit TypeErasedValue(T&& value) 
        : meta_type_(MetaType::from_type<std::decay_t<T>>())
        , value_(std::forward<T>(value)) {}
    
    /// @brief 获取类型信息
    [[nodiscard]] const MetaType& meta_type() const noexcept { return meta_type_; }
    
    /// @brief 获取值
    template<typename T>
    [[nodiscard]] const T& get() const {
        if (meta_type_.id() != get_type_id<T>()) {
            throw std::bad_any_cast{};
        }
        return std::any_cast<const T&>(value_);
    }
    
    /// @brief 设置值
    template<typename T>
    void set(T&& value) {
        meta_type_ = MetaType::from_type<std::decay_t<T>>();
        value_ = std::forward<T>(value);
    }
    
    /// @brief 是否有值
    [[nodiscard]] bool has_value() const noexcept { return value_.has_value(); }
    
    /// @brief 重置
    void reset() noexcept { 
        meta_type_ = MetaType{};
        value_.reset(); 
    }

private:
    MetaType meta_type_;
    std::any value_;
};

} // namespace property::foundation
